import express from 'express';
import { routes } from './src/routes/index_routes';
import { sequelizeInit } from './src/sequelize_init';
import cors from "cors";
import rateLimit from "express-rate-limit";

import { SequelizeAssociations } from './src/sequelize_associations';
import { Middlewares } from './src/middlewares/middlewares';
import * as admin from 'firebase-admin';
import adminJson from "./FirebaseAdmin.json";
import { RepoProvider } from './src/core/RepoProvider';


// //@ts-ignore
// Number.prototype.toJSON = function () {
//   //@ts-ignore
//   return Number(this.toString());
// };

const app = express()
const port = 3002

app.use(cors({
  origin: '*',
}));

app.use(express.json());

const rateLimiter = rateLimit({
  windowMs: 1 * 60 * 1000,
  max: 200,
  message: "Too many requests, please try after some time.",
});

app.use(rateLimiter);

const initApp = () => {

  admin.initializeApp({
    credential: admin.credential.cert(adminJson as admin.ServiceAccount)
  });
  sequelizeInit.authenticate()
    .then(async () => {
      console.log("DB connected successfully");
      console.log("Preparing server...");
      console.log("Starting redis...");

      /* connect redis */
      await RepoProvider.redisServerRepository.connect();

      await SequelizeAssociations.associate();
      // await sequelizeInit.sync();
      console.log("Configuring api routes...");
      for (const route of routes) {
        app.use('/api',
          Middlewares.useAuthMiddleware,
          Middlewares.sessionAuthMiddleware,
          route);
      }
      console.log("Server is ready");
      console.clear();
      app.listen(port, () => {
        console.log(`Server is running on port ${port}`);
      });
    });
}

export { initApp }
