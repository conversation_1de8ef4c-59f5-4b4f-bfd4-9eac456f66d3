'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const tableName = 'raw-materials'; // Ensure this matches your actual table name
    const primaryKeyColumn = 'id'; // IMPORTANT: Replace with your actual primary key column name

    // Step 1: Update "sku" column to lowercase and trim
    console.log(`Step 1: Updating 'sku' column in '${tableName}' to lowercase and trim...`);
    await queryInterface.sequelize.query(
      `UPDATE "${tableName}" SET sku = LOWER(TRIM(sku)) WHERE sku IS NOT NULL;`
    );
    console.log('SKU column updated to lowercase and trimmed.');

    // Step 2: Handle duplicate SKUs by appending _1, _2, etc.
    let duplicatesFound = true;
    let iteration = 0;

    console.log(`Step 2: Handling duplicate SKUs in '${tableName}'...`);

    while (duplicatesFound) {
      iteration++;
      console.log(`  Iteration ${iteration}: Checking for duplicates...`);

      // Find duplicates and assign a row number within each duplicate group
      // CRITICAL: Ensure `type: Sequelize.QueryTypes.SELECT` for consistent array return
      const [rowsToRename, metadata] = await queryInterface.sequelize.query( // Keep metadata even if not used, to ensure destructuring works
        `WITH Duplicates AS (
          SELECT
            ${primaryKeyColumn},
            sku,
            ROW_NUMBER() OVER (PARTITION BY sku ORDER BY ${primaryKeyColumn}) as rn
          FROM "${tableName}"
        )
        SELECT ${primaryKeyColumn}, sku, rn
        FROM Duplicates
        WHERE rn > 1;`, // We only want rows that are duplicates (rn > 1)
        {
          type: Sequelize.QueryTypes.SELECT // <--- THIS IS KEY!
        }
      );
      console.log(rowsToRename);
      

      if (rowsToRename.length === 0) {
        duplicatesFound = false; // No more duplicates found in this iteration
        console.log(`  Iteration ${iteration}: No more duplicates found. Finishing.`);
      } else {
        console.log(`  Iteration ${iteration}: Found ${rowsToRename.length} duplicates to resolve.`);
        for (const row of rowsToRename) {
          // Append the row number to create a unique SKU
          // Use row.rn - 1 to start suffixes from _0 if you prefer, or row.rn for _1.
          // Based on original request _1, _2 etc.
          const suffixNum = row.rn > 0 ? row.rn - 1 : 0; // Ensures _0 for the first duplicate if rn=1
          const newSkuBase = `${row.sku}_${suffixNum}`;

          let finalSku = newSkuBase;
          let uniqueAttempt = 0;
          let isUnique = false;

          // Loop to ensure the generated SKU is truly unique, even if _N already exists
          while (!isUnique) {
            const [exists] = await queryInterface.sequelize.query(
              `SELECT COUNT(*) AS count FROM "${tableName}" WHERE sku = :checkSku;`, // Use an alias for count
              {
                replacements: { checkSku: finalSku },
                type: Sequelize.QueryTypes.SELECT // <--- Also crucial here
              }
            );

            if (exists[0].count === 0) { // Check the count property from the first element of the result array
              isUnique = true;
            } else {
              uniqueAttempt++;
              // If the simple _N suffix is already taken, try a more robust approach
              finalSku = `${newSkuBase}_${uniqueAttempt}`;
              console.warn(`    WARNING: Generated SKU '${newSkuBase}' already exists. Attempting '${finalSku}' for ID ${row[primaryKeyColumn]}.`);
            }
          }

          // Update the SKU for the current duplicate row
          await queryInterface.sequelize.query(
            `UPDATE "${tableName}"
             SET sku = :newSku
             WHERE ${primaryKeyColumn} = :id;`,
            {
              replacements: { newSku: finalSku, id: row[primaryKeyColumn] }
            }
          );
          console.log(`    Updated row ID ${row[primaryKeyColumn]}: original ${row.sku} -> new ${finalSku}`);
        }
      }
    }
    console.log('All duplicate SKU handling complete.');

    // IMPORTANT: After ensuring uniqueness, apply the unique constraint if not already present
    try {
      await queryInterface.addConstraint(tableName, {
        fields: ['sku'],
        type: 'unique',
        name: 'unique_sku_constraint', // Choose a meaningful name
      });
      console.log('Unique constraint on "sku" column added successfully.');
    } catch (error) {
      if (error.name === 'SequelizeUniqueConstraintError' || (error.message && error.message.includes('unique constraint'))) {
        console.warn('Unique constraint "unique_sku_constraint" already exists or could not be added due to existing data (should be resolved by now). Please check manually.');
      } else {
        console.error('Error adding unique constraint on "sku":', error);
        throw error; // Re-throw if it's an unexpected error
      }
    }
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
