'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const tableName = 'raw-materials';
    const primaryKeyColumn = 'id';

    // Step 1: Update "sku" column to lowercase and trim
    console.log(`Step 1: Updating 'sku' column in '${tableName}' to lowercase and trim...`);
    await queryInterface.sequelize.query(
      `UPDATE "${tableName}" SET sku = LOWER(TRIM(sku)) WHERE sku IS NOT NULL;`
    );
    console.log('SKU column updated to lowercase and trimmed.');

    // Step 2: Handle duplicate SKUs by appending _1, _2, etc.
    console.log(`Step 2: Handling duplicate SKUs in '${tableName}'...`);

    // Get all duplicate SKUs with their IDs
    const duplicateRows = await queryInterface.sequelize.query(
      `WITH DuplicateSkus AS (
        SELECT sku, COUNT(*) as dup_count
        FROM "${tableName}"
        WHERE sku IS NOT NULL
        GROUP BY sku
        HAVING COUNT(*) > 1
      ),
      RankedDuplicates AS (
        SELECT
          rm.${primaryKeyColumn},
          rm.sku,
          ROW_NUMBER() OVER (PARTITION BY rm.sku ORDER BY rm.${primaryKeyColumn}) as rn
        FROM "${tableName}" rm
        INNER JOIN DuplicateSkus ds ON rm.sku = ds.sku
      )
      SELECT ${primaryKeyColumn}, sku, rn
      FROM RankedDuplicates
      WHERE rn > 1
      ORDER BY sku, ${primaryKeyColumn};`,
      {
        type: Sequelize.QueryTypes.SELECT
      }
    );

    console.log(`Found ${duplicateRows.length} duplicate rows to process.`);

    // Process each duplicate row
    for (const row of duplicateRows) {
      let finalSku = `${row.sku}_${row.rn - 1}`; // Start with _1, _2, etc.
      let attempt = 0;
      let isUnique = false;

      // Ensure the generated SKU is unique
      while (!isUnique) {
        const existingCount = await queryInterface.sequelize.query(
          `SELECT COUNT(*) as count FROM "${tableName}" WHERE sku = :checkSku;`,
          {
            replacements: { checkSku: finalSku },
            type: Sequelize.QueryTypes.SELECT
          }
        );

        if (existingCount[0].count === 0) {
          isUnique = true;
        } else {
          attempt++;
          finalSku = `${row.sku}_${row.rn - 1}_${attempt}`;
          console.warn(`    SKU '${row.sku}_${row.rn - 1}' already exists. Trying '${finalSku}' for ID ${row[primaryKeyColumn]}.`);
        }
      }

      // Update the SKU for the duplicate row
      await queryInterface.sequelize.query(
        `UPDATE "${tableName}" SET sku = :newSku WHERE ${primaryKeyColumn} = :id;`,
        {
          replacements: { newSku: finalSku, id: row[primaryKeyColumn] }
        }
      );

      console.log(`    Updated row ID ${row[primaryKeyColumn]}: '${row.sku}' -> '${finalSku}'`);
    }

    console.log('All duplicate SKU handling complete.');

    // IMPORTANT: After ensuring uniqueness, apply the unique constraint if not already present
    try {
      await queryInterface.addConstraint(tableName, {
        fields: ['sku'],
        type: 'unique',
        name: 'unique_sku_constraint',
      });
      console.log('Unique constraint on "sku" column added successfully.');
    } catch (error) {
      if (error.name === 'SequelizeUniqueConstraintError' || (error.message && error.message.includes('unique constraint'))) {
        console.warn('Unique constraint "unique_sku_constraint" already exists or could not be added due to existing data (should be resolved by now). Please check manually.');
      } else {
        console.error('Error adding unique constraint on "sku":', error);
        throw error; // Re-throw if it's an unexpected error
      }
    }
  },

  async down(queryInterface, Sequelize) {
    const tableName = 'raw-materials';

    // Remove the unique constraint
    try {
      await queryInterface.removeConstraint(tableName, 'unique_sku_constraint');
      console.log('Unique constraint on "sku" column removed successfully.');
    } catch (error) {
      console.warn('Could not remove unique constraint "unique_sku_constraint". It may not exist.');
    }

    // Note: We cannot easily revert the SKU changes as we don't know the original values
    console.log('Note: SKU changes cannot be automatically reverted. Manual intervention may be required.');
  }
};
