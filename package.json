{"name": "usi-ims", "version": "1.0.0", "description": "Inventory management system", "scripts": {"clear": "lsof -t -i tcp:3002 | xargs kill", "dev": " npm run check && concurrently \"npx tsc --watch\" \"nodemon -q dist/server.js\"", "build": "npm run check && tsc", "start": "node dist/server.js", "migrate": "npx sequelize-cli db:migrate", "seed": "npx sequelize-cli db:seed:all", "check": "tsc --noEmit"}, "author": "", "license": "ISC", "dependencies": {"cors": "2.8.5", "dotenv": "16.4.7", "express": "4.21.2", "express-rate-limit": "7.5.0", "firebase-admin": "13.0.2", "redis": "4.7.0", "json-with-bigint": "2.4.1", "lodash": "4.17.21", "pg": "8.13.1", "pg-hstore": "2.3.4", "sequelize": "6.37.5", "typescript": "5.7.2", "zod": "3.24.1"}, "devDependencies": {"@types/cors": "2.8.17", "@types/express": "5.0.0", "@types/lodash": "4.17.13", "@types/node": "22.10.2", "@types/sequelize": "4.28.20", "concurrently": "9.1.0", "nodemon": "3.1.9", "ts-node-dev": "2.0.0"}}