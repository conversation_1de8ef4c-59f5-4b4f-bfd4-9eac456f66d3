interface AuditMetaData {
    createdById: number;
    createdAt: Date;
    updatedById: number | null;
    updatedAt: Date | null;
    deletedById: number | null;
    deletedAt: Date | null;
}


type AuditMetaKeys = "createdById" | "createdAt" | "updatedById" | "updatedAt" | "deletedById" | "deletedAt";


interface ActionByInfo {
    id: number;
    name: string;
}

interface AuditData {
    auditData: {
        createdBy: ActionByInfo;
        updatedBy: ActionByInfo | null;
        deletedBy: ActionByInfo | null;
    }
}



interface InterfaceMetaData extends AuditMetaData {
    id: number;
}

interface APIBaseResponse<T> {
    success: boolean;
    message: string;
    data: T | null;
}

interface PaginatedBaseResponse<T> {
    totalData: number;
    currentPage: number;
    totalPages: number;
    data: T[];
}






export { InterfaceMetaData, APIBaseResponse, PaginatedBaseResponse, AuditMetaKeys, AuditData };