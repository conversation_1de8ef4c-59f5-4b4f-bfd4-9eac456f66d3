import { NextFunction, Request, Response } from "express";
import { RepoProvider } from "../../../core/RepoProvider";
import { get, pick } from "lodash";
import { ADDRESS_STATUS } from "../models/AddressMisc";
import { IAddress } from "../models/IAddress";

export class AddressController {

    static async create(req: Request, res: Response, next: NextFunction) {

        const user_id = get(req, "user_id", );

        const payload = pick(req.body, ["street", "city", "state", "country", "postalCode", ]) as IAddress;
        payload.status = ADDRESS_STATUS.ACTIVE;
        payload.createdById = Number(user_id!);

        const result = await RepoProvider.addressRepo.create(payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async update(req: Request, res: Response, next: NextFunction) {


        const user_id = get(req, "user_id", );
        const id = Number(get(req.params, "id"));
        const payload = {
            ...req.body,
            updatedById: Number(user_id),
        };

        const result = await RepoProvider.addressRepo.update(id, payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async delete(req: Request, res: Response, next: NextFunction) {
        const ids: any = pick(req.body, "ids");

        const result = await RepoProvider.addressRepo.delete(ids, 1);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getAll(req: Request, res: Response, next: NextFunction) {

        const result = await RepoProvider.addressRepo.getAll(1, 100);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getById(req: Request, res: Response, next: NextFunction) {
        const id = get(req.params, "id");
        const result = await RepoProvider.addressRepo.getById(Number(id));
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }
}