import { NextFunction, Request, Response } from "express";
import { ICreateDebitNote, ICreateDebitNotesData, IMarkDebitNotePaid, IUpdateDebitNote } from "../models/IDebitNote";
import { RepoProvider } from "../../../core/RepoProvider";
import { get } from "lodash";

export class DebitNoteController {

    static async create(req: Request, res: Response, next: NextFunction) {

        // const userId = get(req, "user_id");

        // const payload = pick(req.body, ["name", "status",]) as ICreateDebitNote;
        // payload.status = ITEM_CATEGORY_STAUS.ACTIVE;
        // payload.createdById = Number(userId);
        // payload.name = payload.name.toLowerCase();

        // const result = await RepoProvider.DebitNoteRepo.create(payload);
        // if (!result.success) {
        //     res.status(500).send(result);
        //     return;
        // }
        // res.status(200).send(result);
    }

    static async markPaid(req: Request, res: Response, next: NextFunction) {

        const userId = get(req, "user_id");

        const payload: IMarkDebitNotePaid = {
            purchaseInvoiceId: req.body.purchaseInvoiceId,
            note: req.body.note,
            updatedId: Number(userId!),
        };

        const result = await RepoProvider.debitNoteRepo.markPaid(payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }



    static async getAll(req: Request, res: Response, next: NextFunction) {

        const page = Number(get(req.query, "page"));
        const pageSize = Number(get(req.query, "pageSize"));

        const result = await RepoProvider.debitNoteRepo.getAll(page, pageSize);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getByPurchasInvoiceId(req: Request, res: Response, next: NextFunction) {
        const id = get(req.params, "id");
        const result = await RepoProvider.debitNoteRepo.getByPurchasInvoiceId(Number(id));
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }


    static async searchByText(req: Request, res: Response, next: NextFunction) {

        const text = get(req.query, "text") as string;
        const result = await RepoProvider.debitNoteRepo.searchByText(text);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async createDebitNotes(req: Request, res: Response, next: NextFunction) {
        const userId = get(req, "user_id");
        const payload: ICreateDebitNotesData ={ ...req.body, createdById:Number(userId!)};
        const result = await RepoProvider.debitNoteRepo.createDebitNotes(payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async updateDebitNotes(req: Request, res: Response, next: NextFunction) {

        const userId = get(req, "user_id");
        const payload: IUpdateDebitNote = {
            ...req.body,
            updatedById: Number(userId!),
        };
        const id = Number(get(req.params, "id"));
        const result = await RepoProvider.debitNoteRepo.updateDebitNotes(id, payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

}