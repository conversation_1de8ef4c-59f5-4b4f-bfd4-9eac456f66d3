import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { DebitNoteTable } from "../database/DebitNoteTable";
import { ICreateDebitNote, ICreateDebitNotesData, IDebitNoteOverviewResponse, IDebitNoteResponse, IMarkDebitNotePaid, IUpdateDebitNote, } from "../models/IDebitNote";

export interface IDebitNoteRepo {
    create(payload: ICreateDebitNote): Promise<APIBaseResponse<DebitNoteTable | null>>;

    getAll(page: number, pageSize: number): Promise<APIBaseResponse<PaginatedBaseResponse<IDebitNoteOverviewResponse> | null>>;

    searchByText(text: string): Promise<APIBaseResponse<PaginatedBaseResponse<IDebitNoteOverviewResponse> | null>>;

    getByPurchasInvoiceId(purchaseInvoiceId: number): Promise<APIBaseResponse<IDebitNoteResponse[] | null>>;

    markPaid(payload: IMarkDebitNotePaid): Promise<APIBaseResponse<null>>;

    createDebitNotes(payload: ICreateDebitNotesData): Promise<APIBaseResponse<DebitNoteTable | null>>

    updateDebitNotes(id: Number, payload: IUpdateDebitNote): Promise<APIBaseResponse<DebitNoteTable | null>>

}