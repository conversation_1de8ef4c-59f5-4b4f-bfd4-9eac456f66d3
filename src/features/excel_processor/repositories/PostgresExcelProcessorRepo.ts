import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { HelperMethods } from "../../../core/HelperMethods";
import { PaginationProvider } from "../../pagination/PaginationProvider";
import { CoreUserTable } from "../../../features/users/core/database/CoreUserTable";
import { IExcelProcessorRepo } from "./IExcelProcessorRepo";
import { IExcelProcessorRequest, IExcelProcessorResponse } from "../models/IExcelProcessor";
import { Op, WhereOptions } from "sequelize";
import { sequelizeInit } from "../../../sequelize_init";
import { ExcelProcessorTable } from "../database/ExcelProcessorTable";

export class PostgresExcelProcessorRepo implements IExcelProcessorRepo {
  async processExcel(excelProcessor: IExcelProcessorRequest): Promise<APIBaseResponse<IExcelProcessorResponse | null>> {
    const transaction = await sequelizeInit.transaction();

    try {
      const result = await ExcelProcessorTable.create({
        fileName: excelProcessor.file.originalname,
        processedAt: new Date(),
        createdById: excelProcessor.createdById,
      }, {
        transaction: transaction,
        userId: excelProcessor.createdById,
      });

      await transaction.commit();

      const response: IExcelProcessorResponse = {
        id: Number(result.getDataValue('id')),
        fileName: result.getDataValue('fileName'),
        processedAt: result.getDataValue('processedAt'),
        createdBy: "",
        createdAt: result.getDataValue('createdAt'),
      };

      return HelperMethods.getSuccessResponse(response);
    } catch (error) {
      await transaction.rollback();
      HelperMethods.handleError(error);
      return HelperMethods.getErrorResponse();
    }
  }

  async getAll(page: number, pageSize: number, text?: string, startDate?: Date, endDate?: Date): Promise<APIBaseResponse<PaginatedBaseResponse<IExcelProcessorResponse> | null>> {
    try {
      const whereConditions: WhereOptions<any> = {};

      if (text) {
        whereConditions.fileName = {
          [Op.iLike]: `%${text}%`
        };
      }

      const includeData = [
        {
          model: CoreUserTable,
          as: 'createdBy',
          required: true,
        }
      ];

      const paginatedData = await new PaginationProvider<any, ExcelProcessorTable>().getPaginatedRecords(
        ExcelProcessorTable,
        {
          include: includeData,
          page: page,
          limit: pageSize,
          dateColumn: "createdAt",
          startDate: startDate ?? undefined,
          endDate: endDate ?? undefined,
          order: [['createdAt', 'DESC']]
        }
      );

      const data: IExcelProcessorResponse[] = paginatedData.rows.map(row => {
        return {
          id: Number(row.dataValues.id),
          fileName: row.dataValues.fileName,
          processedAt: row.dataValues.processedAt,
          createdBy: row.createdBy.dataValues.firstName,
          createdAt: row.dataValues.createdAt,
        };
      });

      return HelperMethods.getSuccessResponse({
        data: data,
        totalData: paginatedData.total,
        totalPages: paginatedData.totalPages,
        currentPage: paginatedData.currentPage,
      });
    } catch (error) {
      HelperMethods.handleError(error);
      return HelperMethods.getErrorResponse();
    }
  }

  async getById(id: number): Promise<APIBaseResponse<IExcelProcessorResponse | null>> {
    try {
      const result = await ExcelProcessorTable.findByPk(id, {
        include: [
          {
            model: CoreUserTable,
            as: 'createdBy',
            required: true,
          }
        ]
      });

      if (!result) {
        return HelperMethods.getErrorResponse("Excel processor record not found");
      }

      const response: IExcelProcessorResponse = {
        id: Number(result.dataValues.id),
        fileName: result.dataValues.fileName,
        processedAt: result.dataValues.processedAt,
        createdBy: result.createdBy.dataValues.firstName,
        createdAt: result.dataValues.createdAt,
      };

      return HelperMethods.getSuccessResponse(response);
    } catch (error) {
      HelperMethods.handleError(error);
      return HelperMethods.getErrorResponse();
    }
  }
}
