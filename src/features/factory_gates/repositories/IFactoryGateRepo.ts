import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { FactoryGateTable } from "../database/FactoryGateTable";
import { ICreateFactoryGate } from "../models/IFactoryGate";

export interface IFactoryGateRepo {
    create(vendor: ICreateFactoryGate): Promise<APIBaseResponse<FactoryGateTable | null>>;

    update(id: number, Vendor: ICreateFactoryGate): Promise<APIBaseResponse<void>>;

    getAll(page: number, pageSize: number): Promise<APIBaseResponse<PaginatedBaseResponse<FactoryGateTable> | null>>;

    searchByText(text: string): Promise<APIBaseResponse<PaginatedBaseResponse<FactoryGateTable> | null>>;

    getById(id: number): Promise<APIBaseResponse<FactoryGateTable | null>>;

    delete(ids: Number[], deletedById: Number): Promise<APIBaseResponse<null>>;
}