import { NextFunction, Request, Response } from 'express'
import { RepoProvider } from '../../../core/RepoProvider'
import { get, pick } from 'lodash'
import { ITEM_ATTRIBUTES_STATUS } from '../models/ItemAttributeMisc'
import {
  CreateItemAttribute,
  UpdateItemAttribute,
} from '../models/IItemAttribute'

export class ItemAttributeController {

    static async create(req: Request, res: Response, next: NextFunction) {

        const user_id = get(req, "user_id");

        const payload: CreateItemAttribute = {
            name: req.body.name,
            status: ITEM_ATTRIBUTES_STATUS.ACTIVE,
            createdById: Number(user_id!),
        };

    const result = await RepoProvider.itemAttributeRepo.create(payload)
    if (!result.success) {
      res.status(500).send(result)
      return
    }
    res.status(200).send(result)
  }

    static async update(req: Request, res: Response, next: NextFunction) {

        const id = Number(get(req.params, "id"));
        const user_id = get(req, "user_id",);


        const payload: UpdateItemAttribute = {
            id: id,
            name: req.body.name,
            status: req.body.status,
            updatedById: Number(user_id!),
        };

    const result = await RepoProvider.itemAttributeRepo.update(payload)
    if (!result.success) {
      res.status(500).send(result)
      return
    }
    res.status(200).send(result)
  }



  static async getAll(req: Request, res: Response, next: NextFunction) {
    const page = Number(get(req.query, 'page'))
    const pageSize = Number(get(req.query, 'pageSize'))
    const text = get(req.query, 'text') as string
    const result = await RepoProvider.itemAttributeRepo.getAll(
      page,
      pageSize,
      text
    )
    if (!result.success) {
      res.status(500).send(result)
      return
    }
    res.status(200).send(result)
  }

    static async getById(req: Request, res: Response, next: NextFunction) {
        const id = get(req.params, "id");
        const result = await RepoProvider.itemAttributeRepo.getById(Number(id));
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }
}
