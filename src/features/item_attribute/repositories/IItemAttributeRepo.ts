import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { ItemAttributeTable } from "../database/ItemAttributeTable";
import { CreateItemAttribute, UpdateItemAttribute, } from "../models/IItemAttribute";

export interface IItemAttributeRepo {
    create(payload: CreateItemAttribute): Promise<APIBaseResponse<ItemAttributeTable | null>>;

    update(payload: UpdateItemAttribute): Promise<APIBaseResponse<null>>;

    getAll(page: number, pageSize: number, text?: string): Promise<APIBaseResponse<PaginatedBaseResponse<ItemAttributeTable> | null>>;

    getById(id: Number): Promise<APIBaseResponse<ItemAttributeTable | null>>;

    delete(ids: Number[], deletedById: Number): Promise<APIBaseResponse<null>>;
}