import {
  APIBaseResponse,
  PaginatedBaseResponse,
} from '../../../core/CoreInterfaces'
import { HelperMethods } from '../../../core/HelperMethods'
import { sequelizeInit } from '../../../sequelize_init'
import { ItemAttributeTable } from '../database/ItemAttributeTable'
import {
  CreateItemAttribute,
  IItemAttribute,
  UpdateItemAttribute,
} from '../models/IItemAttribute'
import { ITEM_ATTRIBUTES_STATUS } from '../models/ItemAttributeMisc'
import { IItemAttributeRepo } from './IItemAttributeRepo'
import { Op, UniqueConstraintError, WhereOptions } from 'sequelize'

export class PostgresItemAttributeRepo implements IItemAttributeRepo {
  async create(
    payload: CreateItemAttribute
  ): Promise<APIBaseResponse<ItemAttributeTable | null>> {
    try {
      const result = await ItemAttributeTable.create(payload, {
        userId: payload.createdById,
      })
      return HelperMethods.getSuccessResponse(
        result,
        'Attribute created successfully'
      )
    } catch (error) {
      HelperMethods.handleError(error)
      if (error instanceof UniqueConstraintError) {
        return HelperMethods.getErrorResponse('Attribute already exists')
      }
      return HelperMethods.getErrorResponse()
    }
  }

  async update(payload: UpdateItemAttribute): Promise<APIBaseResponse<null>> {
    try {
      await ItemAttributeTable.update(payload, {
        where: {
          id: payload.id,
        },
        userId: payload.updatedById!,
      })

      return HelperMethods.getSuccessResponse(
        null,
        'Attribute Updated Successfully'
      )
    } catch (error) {
      HelperMethods.handleError(error)
      if (error instanceof UniqueConstraintError) {
        return HelperMethods.getErrorResponse('Attribute already exists')
      }
      return HelperMethods.getErrorResponse()
    }
  }

  async getAll(
    page: number,
    pageSize: number,
    text?: string
  ): Promise<
    APIBaseResponse<PaginatedBaseResponse<ItemAttributeTable> | null>
  > {
    try {
      const whereConditions: WhereOptions<IItemAttribute> = {
        status: ITEM_ATTRIBUTES_STATUS.ACTIVE,
      }

      if (text) {
        whereConditions.name = {
          [Op.iLike]: text,
        }
      }
      const offset = (page - 1) * pageSize
      const { count, rows } = await ItemAttributeTable.findAndCountAll({
        limit: pageSize,
        offset: offset,
        order: [['createdAt', 'DESC']],
        where: whereConditions,
      })

      const totalPages = Math.ceil(count / pageSize)
      return HelperMethods.getSuccessResponse(
        {
          currentPage: page,
          totalData: count,
          totalPages: totalPages,
          data: rows,
        },
        'Attributes Retrieved Successfully'
      )
    } catch (error) {
      HelperMethods.handleError(error)
      return HelperMethods.getErrorResponse()
    }
  }

    async getById(id: number): Promise<APIBaseResponse<ItemAttributeTable | null>> {
        try {
            const result = await ItemAttributeTable.findByPk(id);
            if (!result) {
                return HelperMethods.getErrorResponse('Item Attribute not found');
            }
            return HelperMethods.getSuccessResponse(result, "Item Attribute Retrieved Successfully");
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async delete(ids: number[], deletedById: number): Promise<APIBaseResponse<null>> {
        try {
            const transaction = await sequelizeInit.transaction();
            await ItemAttributeTable.update({
                status: ITEM_ATTRIBUTES_STATUS.DELETED,
                deletedById: deletedById,
            }, {
                where: {
                    id: {
                        [Op.in]: ids
                    }
                },
                transaction: transaction,
                userId: deletedById,
            });
            return HelperMethods.getSuccessResponse(null, "Item Attribute deleted successfully");
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }
}
