import { NextFunction, Request, Response } from "express";
import { RepoProvider } from "../../../core/RepoProvider";
import { get, pick } from "lodash";
import { ITEM_ATTRIBUTES_VALUE_STATUS } from "../models/ItemAttributeValueMisc";
import { CreateItemAttributeValue, ItemAttributeValuePayload, UpdateItemAttributeValue } from "../models/IItemAttributeValue";

export class ItemAttributeValueController {

    static async create(req: Request, res: Response, next: NextFunction) {

        const user_id = get(req, "user_id",);

        const requestBody = req.body as ItemAttributeValuePayload;

        const payload: CreateItemAttributeValue[] = requestBody.itemAttributeValues.map(data => {
            return {
                title: data.title,
                value: data.value,
                itemAttributeId: requestBody.itemAttributeId,
                status: ITEM_ATTRIBUTES_VALUE_STATUS.ACTIVE,
                createdById: Number(user_id!),
            }
        })
        const result = await RepoProvider.itemAttributeValueRepo.create(payload, user_id!);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async update(req: Request, res: Response, next: NextFunction) {

        const id = Number(get(req.params, "id"));

        const user_id = get(req, "user_id",);

        const requestBody = req.body as UpdateItemAttributeValue;
        const payload: UpdateItemAttributeValue = {
            id: id,
            title: requestBody.title,
            value: requestBody.value,
            itemAttributeId: requestBody.itemAttributeId,
            status: requestBody.status,
            updatedById: Number(user_id!),
        };

        const result = await RepoProvider.itemAttributeValueRepo.update(payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getAll(req: Request, res: Response, next: NextFunction) {
        const page = Number(get(req.query, "page"));
        const pageSize = Number(get(req.query, "pageSize"));
        const text = get(req.query, "text") as string;
        const result = await RepoProvider.itemAttributeValueRepo.getAll(page, pageSize, text);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getById(req: Request, res: Response, next: NextFunction) {
        const id = get(req.params, "id");
        const result = await RepoProvider.itemAttributeValueRepo.getById(Number(id));
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }
}