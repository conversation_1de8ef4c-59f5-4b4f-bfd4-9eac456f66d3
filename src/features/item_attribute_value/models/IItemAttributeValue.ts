import { InterfaceMetaData } from "../../../core/CoreInterfaces";
import { ITEM_ATTRIBUTES_VALUE_STATUS } from "./ItemAttributeValueMisc";

interface IItemAttributeValue extends InterfaceMetaData {
    id: number;
    itemAttributeId: number;
    value: string;
    title: string;
    status: ITEM_ATTRIBUTES_VALUE_STATUS;
}

type CreateItemAttributeValue = Pick<IItemAttributeValue, 'itemAttributeId' | 'value' | 'title' | 'createdById' | 'status'>;


type UpdateItemAttributeValue = Pick<IItemAttributeValue, 'id' | 'itemAttributeId' | 'value' | 'title' | 'status' | 'updatedById'>;


type ItemAttributeValue = {
    title: string;
    value: string;
};

type ItemAttributeValuePayload = {
    itemAttributeId: number;
    itemAttributeValues: ItemAttributeValue[];
};


export { IItemAttributeValue, CreateItemAttributeValue, UpdateItemAttributeValue, ItemAttributeValuePayload };