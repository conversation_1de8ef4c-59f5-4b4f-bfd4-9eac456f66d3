import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { ItemAttributeValueTable } from "../database/ItemAttributeValueTable";
import { CreateItemAttributeValue, UpdateItemAttributeValue } from "../models/IItemAttributeValue";

export interface IItemAttributeValueRepo {
    create(payload: CreateItemAttributeValue[], userId:Number): Promise<APIBaseResponse<ItemAttributeValueTable[] | null>>;

    update(payload: UpdateItemAttributeValue): Promise<APIBaseResponse<void>>;

    getAll(page: number, pageSize: number, text?: string): Promise<APIBaseResponse<PaginatedBaseResponse<ItemAttributeValueTable> | null>>;

    getById(id: Number): Promise<APIBaseResponse<ItemAttributeValueTable | null>>;

    delete(ids: Number[], deletedById: Number): Promise<APIBaseResponse<null>>;
}