import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { HelperMethods } from "../../../core/HelperMethods";
import { sequelizeInit } from "../../../sequelize_init";
import { ItemAttributeTable } from "../../item_attribute/database/ItemAttributeTable";
import { ItemAttributeValueTable } from "../database/ItemAttributeValueTable";
import { CreateItemAttributeValue, IItemAttributeValue, UpdateItemAttributeValue } from "../models/IItemAttributeValue";
import { ITEM_ATTRIBUTES_VALUE_STATUS } from "../models/ItemAttributeValueMisc";
import { IItemAttributeValueRepo } from "./IItemAttributeValueRepo";
import { Op, UniqueConstraintError, WhereOptions } from "sequelize";

export class PostgresItemAttributeValueRepo implements IItemAttributeValueRepo {

    async create(payload: CreateItemAttributeValue[], userId: Number): Promise<APIBaseResponse<ItemAttributeValueTable[] | null>> {
        try {
            /* check if attribute exists */
            const existingAttribute = await ItemAttributeTable.findOne({
                where: {
                    id: payload[0].itemAttributeId
                }
            });

            if (!existingAttribute) {
                return HelperMethods.getErrorResponse('Item Attribute does not exist');
            }


            const result = await ItemAttributeValueTable.bulkCreate(payload, {
                userId
            });
            return HelperMethods.getSuccessResponse(result, "Item Attribute Value created successfully");
        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                return HelperMethods.getErrorResponse('Some or All, Item Attribute Values already exist');
            }
            return HelperMethods.getErrorResponse();
        }
    }
    async update(payload: UpdateItemAttributeValue): Promise<APIBaseResponse<null>> {
        try {
            /* check if attribute value exists */
            const existingValue = await ItemAttributeValueTable.findByPk(
                payload.id
            );
            if (!existingValue) {
                return HelperMethods.getErrorResponse('Item Attribute Value does not exist');
            }
            /* check if attribute exists */
            const existingAttribute = await ItemAttributeTable.findOne({
                where: {
                    id: payload.itemAttributeId
                }
            });

            if (!existingAttribute) {
                return HelperMethods.getErrorResponse('Item Attribute does not exist');
            }


            await ItemAttributeValueTable.update(payload, {
                where: {
                    id: payload.id
                },
                userId: payload.updatedById!,
            });

            return HelperMethods.getSuccessResponse(null, "Item Attribute Value updated successfully");
        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                return HelperMethods.getErrorResponse('Item Attribute Value already exist');
            }
            return HelperMethods.getErrorResponse();
        }
    }
    async getAll(page: number, pageSize: number, text?: string): Promise<APIBaseResponse<PaginatedBaseResponse<ItemAttributeValueTable> | null>> {
        try {
            const whereConditions: WhereOptions<IItemAttributeValue> = {

                status: ITEM_ATTRIBUTES_VALUE_STATUS.ACTIVE
            }

            if (text) {
                whereConditions.title = {
                    [Op.iLike]: text
                };
            }

            const offset = (page - 1) * pageSize;
            const { count, rows } = await ItemAttributeValueTable.findAndCountAll({
                limit: pageSize,
                offset: offset,
                order: [['createdAt', 'DESC']],
                where: whereConditions,
            });

            const totalPages = Math.ceil(count / pageSize);

            return HelperMethods.getSuccessResponse({
                currentPage: page,
                totalData: count,
                totalPages: totalPages,
                data: rows,
            }, "Item Attribute Values retrieved successfully");
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }
    async getById(id: number): Promise<APIBaseResponse<ItemAttributeValueTable | null>> {
        try {
            const result = await ItemAttributeValueTable.findByPk(id);
            return HelperMethods.getSuccessResponse(result, "Item Attribute Value retrieved successfully");
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }
    async delete(ids: number[], deletedById: number): Promise<APIBaseResponse<null>> {
        try {
            const transaction = await sequelizeInit.transaction();
            await ItemAttributeValueTable.update({
                status: ITEM_ATTRIBUTES_VALUE_STATUS.DELETED,
                deletedById: deletedById,
            }, {
                where: {
                    id: {
                        [Op.in]: ids
                    }
                },
                transaction: transaction,
                userId: deletedById,
            });
            return HelperMethods.getSuccessResponse(null, " Item Attribute Value deleted successfully");
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }
}