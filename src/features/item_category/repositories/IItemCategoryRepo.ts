import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { ItemCategoryTable } from "../database/ItemCategoryTable";
import { ICreateItemCategory, } from "../models/IItemCategory";

export interface IItemCategoryRepo {
    create(payload: ICreateItemCategory): Promise<APIBaseResponse<ItemCategoryTable | null>>;

    update(id: Number, payload: ICreateItemCategory): Promise<APIBaseResponse<void>>;

    getAll(page: number, pageSize: number): Promise<APIBaseResponse<PaginatedBaseResponse<ItemCategoryTable> | null>>;

    searchByText(text: string): Promise<APIBaseResponse<PaginatedBaseResponse<ItemCategoryTable> | null>>;

    getById(id: number): Promise<APIBaseResponse<ItemCategoryTable | null>>;

    delete(ids: number[], deletedById: number): Promise<APIBaseResponse<null>>;

    search(text: string): Promise<APIBaseResponse<ItemCategoryTable[] | null>>
}