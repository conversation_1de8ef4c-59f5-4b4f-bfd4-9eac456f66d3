import { NextFunction, Request, Response } from "express";
import { RepoProvider } from "../../../core/RepoProvider";
import { get, pick } from "lodash";
import { ITEM_UNIT_STAUS } from "../models/ItemUnitMisc";
import { ICreateItemUnit } from "../models/IItemUnit";

export class ItemUnitController {

    static async create(req: Request, res: Response, next: NextFunction) {

        const userId = get(req, "user_id",);

        const payload = pick(req.body, ["name", "status",]) as ICreateItemUnit;
        payload.status = ITEM_UNIT_STAUS.ACTIVE;
        payload.createdById = Number(userId!);
        payload.name = payload.name.toLowerCase().trim();
        const result = await RepoProvider.itemUnitRepo.create(payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async update(req: Request, res: Response, next: NextFunction) {

        const id = Number(get(req.params, "id"));

        const userId = get(req, "user_id",);


        const payload = {
            ...req.body,
            updatedById: Number(userId),
        };

        const result = await RepoProvider.itemUnitRepo.update(id, payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async delete(req: Request, res: Response, next: NextFunction) {
        const ids: any = pick(req.body, "ids");

        const userId = get(req, "user_id",);


        const result = await RepoProvider.itemUnitRepo.delete(ids, Number(userId));
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getAll(req: Request, res: Response, next: NextFunction) {

        const page = Number(get(req.query, "page"));
        const pageSize = Number(get(req.query, "pageSize"));

        const result = await RepoProvider.itemUnitRepo.getAll(page, pageSize);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getById(req: Request, res: Response, next: NextFunction) {
        const id = get(req.params, "id");
        const result = await RepoProvider.itemUnitRepo.getById(Number(id));
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async searchByText(req: Request, res: Response, next: NextFunction) {
        const text = get(req.query, "text") as string;
        const result = await RepoProvider.itemUnitRepo.searchByText(text);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }
}