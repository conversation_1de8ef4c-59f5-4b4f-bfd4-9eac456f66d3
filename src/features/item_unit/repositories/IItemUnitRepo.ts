import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { ItemUnitTable } from "../database/ItemUnitTable";
import { ICreateItemUnit, } from "../models/IItemUnit";

export interface IItemUnitRepo {
    create(payload: ICreateItemUnit): Promise<APIBaseResponse<ItemUnitTable | null>>;

    update(id: Number, payload: ICreateItemUnit): Promise<APIBaseResponse<void>>;

    getAll(page: number, pageSize: number): Promise<APIBaseResponse<PaginatedBaseResponse<ItemUnitTable> | null>>;

    searchByText(text: string): Promise<APIBaseResponse<PaginatedBaseResponse<ItemUnitTable> | null>>;

    getById(id: number): Promise<APIBaseResponse<ItemUnitTable | null>>;

    delete(ids: number[], deletedById: number): Promise<APIBaseResponse<null>>;
}