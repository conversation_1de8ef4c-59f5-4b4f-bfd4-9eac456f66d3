import { NextFunction, Request, Response } from "express";
import { HelperMethods } from "../../../core/HelperMethods";
import { RepoProvider } from "../../../core/RepoProvider";
import { get } from "lodash";

export class LogsController {
    static async getAll(req: Request, res: Response, next: NextFunction) {
        try {

            const page = Number(get(req.query, "page"));
            const pageSize = Number(get(req.query, "pageSize"));




            const result = await RepoProvider.logRepo.getAllLogs(page, pageSize);
            if (!result.success) {
                res.status(500).send(result);
                return;
            }
            res.status(200).send(result);
        } catch (error) {
            HelperMethods.handleError(error);
        }
    }
}