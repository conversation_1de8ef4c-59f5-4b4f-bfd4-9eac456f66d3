import { DataTypes, Model } from "sequelize";
import { sequelizeInit } from "../../../sequelize_init";
import { ICreateLog, ILog } from "../models/ILogs";

class LogsTable extends Model<ILog, ICreateLog> {
}

LogsTable.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      get() {
        const value = this.dataValues.id;
        if (value) {
          return Number(value);
        }
      },
    },

    action: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    model: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    recordId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      get() {
        const value = this.dataValues.recordId;
        if (value) {
          return Number(value);
        }
      },
    },
    data: {
      type: DataTypes.JSONB,
      allowNull: false,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: sequelizeInit,
    tableName: "logs",
  }
);

export { LogsTable };
