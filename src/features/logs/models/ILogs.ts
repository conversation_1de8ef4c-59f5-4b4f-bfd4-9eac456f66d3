interface ICreateLog {

  action: string;
  model: string;
  recordId: number;
  data: Record<string, any>;
  userId: number;
  createdAt: Date;
}

interface ILog extends ICreateLog {
  id: number;
}


declare module "sequelize" {
  interface CreateOptions {
    userId: Number;
  }

  interface BulkCreateOptions {
    userId: Number;
  }

  interface UpdateOptions {
    userId: Number;
  }

  interface DestroyOptions {
    userId: Number;
  }
}
export { ILog, ICreateLog };