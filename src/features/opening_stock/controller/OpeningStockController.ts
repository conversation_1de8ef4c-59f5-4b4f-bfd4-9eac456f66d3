import { NextFunction, Request, Response } from 'express'
import { RepoProvider } from '../../../core/RepoProvider'
import { get, pick } from 'lodash'
import { IOpeningStockRepo } from '../repositories/IOpeningStockRepo'
import { IOpeningStockRequest, IUpdateOpeningStockRequest } from '../models/IOpeningStock'

export class OpeningStockController {
  static async create(req: Request, res: Response, next: NextFunction) {

    const userId = get(req, 'user_id')

    const requestBody = req.body as IOpeningStockRequest;

    const payload: IOpeningStockRequest = {
      date: requestBody.date,
      data: requestBody.data,
      createdById: Number(userId!),
    };

    const result = await RepoProvider.openingStockRepo.create(payload)

    if (!result.success) {
      res.status(500).send(result)
      return
    }
    res.status(200).send(result)
  }

  static async update(req: Request, res: Response, next: NextFunction) {

    const userId = get(req, 'user_id')

    const requestBody = req.body

    const payload: IUpdateOpeningStockRequest = {
      data: requestBody.data,
      updatedById: Number(userId!),
    };

    const result = await RepoProvider.openingStockRepo.update(payload)

    if (!result.success) {
      res.status(500).send(result)
      return
    }
    res.status(200).send(result)
  }

  static async getAll(req: Request, res: Response, next: NextFunction) {
    const page = Number(get(req.query, 'page'));
    const pageSize = Number(get(req.query, 'pageSize'));
    const text = get(req.query, 'text') as string | undefined;

    const startDate = (get(req.query, "startDate") === "undefined" ? undefined : get(req.query, "startDate") ?? undefined) as Date | undefined;
    const endDate = (get(req.query, "endDate") === "undefined" ? undefined : get(req.query, "endDate") ?? undefined) as Date | undefined;

    const result = await RepoProvider.openingStockRepo.getAll(page, pageSize, text, startDate, endDate)
    if (!result.success) {
      res.status(500).send(result)
      return
    }
    res.status(200).send(result)
  }

  static async getById(req: Request, res: Response, next: NextFunction) {
    const id = Number(req.params.id)

    const result = await RepoProvider.openingStockRepo.getById(id)
    if (!result.success) {
      res.status(500).send(result)
      return
    }
    res.status(200).send(result)
  }
}
