import { NextFunction, Request, Response } from "express";
import { RepoProvider } from "../../../core/RepoProvider";
import { get } from "lodash";
import { IPurchaseInvoiceAddNewItemRequest, IPurchaseInvoiceRequest, IPurchaseInvoiceUpdateRequest } from "../models/IPurchaseInvoice";
import { PURCHASE_INVOICE_STATUS } from "../models/PurchaseInvoiceMisc";

export class PurchaseInvoiceController {

    static async create(req: Request, res: Response, next: NextFunction) {

        const userId = get(req, "user_id",);

        const payload: IPurchaseInvoiceRequest = {
            invoiceNumber: req.body.invoiceNumber,
            invoiceDate: req.body.invoiceDate,
            supplierId: req.body.supplierId,
            status: PURCHASE_INVOICE_STATUS.ACTIVE,
            createdById: Number(userId!),
            factoryGateId: req.body.factoryGateId,
            rawMaterials: req.body.rawMaterials,
            poNumber: req.body.poNumber,
            poDate: req.body.poDate,
            purchasedById: req.body.purchasedById,
            purchaseOrderId: req.body.purchaseOrderId,
        };

        const result = await RepoProvider.purchaseInvoiceRepo.create(payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }
    static async update(req: Request, res: Response, next: NextFunction) {

        const id = Number(get(req.params, "id"));
        const userId = get(req, "user_id",);

        const payload: IPurchaseInvoiceUpdateRequest = {
            id: id,
            invoiceNumber: req.body.invoiceNumber,
            invoiceDate: req.body.invoiceDate,
            supplierId: req.body.supplierId,
            status: PURCHASE_INVOICE_STATUS.ACTIVE,
            updatedById: Number(userId!),
            factoryGateId: req.body.factoryGateId,
            rawMaterials: req.body.rawMaterials,
            poNumber: req.body.poNumber,
            poDate: req.body.poDate,
            purchasedById: req.body.purchasedById,
        };

        const result = await RepoProvider.purchaseInvoiceRepo.update(payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }
    static async edit(req: Request, res: Response, next: NextFunction) {

        const id = Number(get(req.params, "id"));
        const userId = get(req, "user_id",);

        const payload: IPurchaseInvoiceUpdateRequest = {
            id: id,
            invoiceNumber: req.body.invoiceNumber,
            invoiceDate: req.body.invoiceDate,
            supplierId: req.body.supplierId,
            status: PURCHASE_INVOICE_STATUS.ACTIVE,
            updatedById: Number(userId!),
            factoryGateId: req.body.factoryGateId,
            rawMaterials: req.body.rawMaterials,
            poNumber: req.body.poNumber,
            poDate: req.body.poDate,
            purchasedById: req.body.purchasedById,
        };

        const result = await RepoProvider.purchaseInvoiceRepo.edit(payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getAll(req: Request, res: Response, next: NextFunction) {

        const page = Number(get(req.query, "page"));
        const pageSize = Number(get(req.query, "pageSize"));

        const result = await RepoProvider.purchaseInvoiceRepo.getAll(page, pageSize);

        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getById(req: Request, res: Response, next: NextFunction) {
        const id = get(req.params, "id");
        const result = await RepoProvider.purchaseInvoiceRepo.getById(Number(id));
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async addNewItem(req: Request, res: Response, next: NextFunction) {
        const id = get(req.params, "id");
        const userId = get(req, "user_id");

        const payload: IPurchaseInvoiceAddNewItemRequest = {
            id: Number(id),
            updatedById: Number(userId!),
            rawMaterials: req.body.rawMaterials,
        };
        const result = await RepoProvider.purchaseInvoiceRepo.addNewItem(payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async searchByAny(req: Request, res: Response) {
        const text = req.query.text;
        const page = Number(get(req.query, "page"));
        const pageSize = Number(get(req.query, "pageSize"));
        const result = await RepoProvider.purchaseInvoiceRepo.searchByAny(text as string, page, pageSize);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }


    static async getByDateRange(req: Request, res: Response, next: NextFunction) {

        const startDateString = get(req.query, "startDate") as string;
        const endDateString = get(req.query, "endDate") as string;

        const startDateData = startDateString.split("-");
        const endDateData = endDateString.split("-");

        const startDate = new Date(Number(startDateData[0]), Number(startDateData[1]) - 1, Number(startDateData[2]));
        const endDate = new Date(Number(endDateData[0]), Number(endDateData[1]) - 1, Number(endDateData[2]));

        const page = Number(get(req.query, "page"));
        const pageSize = Number(get(req.query, "pageSize"));


        const result = await RepoProvider.purchaseInvoiceRepo.getByDateRange(
            startDate,
            endDate,
            page, pageSize,);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

}