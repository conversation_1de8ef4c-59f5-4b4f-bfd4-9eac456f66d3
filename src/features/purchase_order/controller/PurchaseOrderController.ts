import { NextFunction, Request, Response } from 'express'
import { RepoProvider } from '../../../core/RepoProvider'
import { get } from 'lodash'
import {
  IPurchaseOrderPayload,
  IUpdatePurchaseOrderPayload,
  UpdatePurchaseOrder,
} from '../models/PurchaseOrder'

export class PurchaseOrderController {

    static async create(req: Request, res: Response, next: NextFunction) {

        const user_id = get(req, "user_id");

        const payload: IPurchaseOrderPayload = {
            ...req.body,
            createdById: Number(user_id!),
        };

      

    const result = await RepoProvider.purchaseOrderRepo.create(payload)
    if (!result.success) {
      res.status(500).send(result)
      return
    }
    res.status(200).send(result)
  }

    static async update(req: Request, res: Response, next: NextFunction) {

        const id = Number(get(req.params, "id"));
        const user_id = get(req, "user_id",);

        const payload: IUpdatePurchaseOrderPayload = {
            ...req.body,
            id,
            updatedById: Number(user_id!),
        };

    const result = await RepoProvider.purchaseOrderRepo.update(payload)
    if (!result.success) {
      res.status(500).send(result)
      return
    }
    res.status(200).send(result)
  }



  static async getAll(req: Request, res: Response, next: NextFunction) {
    const page = Number(get(req.query, 'page'))
    const pageSize = Number(get(req.query, 'pageSize'))
    const text = get(req.query, 'text') as string
    const result = await RepoProvider.purchaseOrderRepo.getAll(
      page,
      pageSize,
      text
    )
    if (!result.success) {
      res.status(500).send(result)
      return
    }
    res.status(200).send(result)
  }

    static async getById(req: Request, res: Response, next: NextFunction) {
        const id = get(req.params, "id");
        const result = await RepoProvider.purchaseOrderRepo.getById(Number(id));
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async delete(req: Request, res: Response, next: NextFunction) {
      const ids: number [] = get(req.body, "ids");
      const user_id = get(req, "user_id");
      
      const convertedIds = ids.map((id) => Number(id));
      const result = await RepoProvider.purchaseOrderRepo.delete(convertedIds, Number(user_id!));
      if (!result.success) {
          res.status(500).send(result);
          return;
      }
      res.status(200).send(result);
  }

  static async searchByPoNumber(req: Request, res: Response, next: NextFunction) {
    const text = get(req.query, "text") as string;
    const result = await RepoProvider.purchaseOrderRepo.searchByPoNumber(text);
    if (!result.success) {
        res.status(500).send(result);
        return;
    }
    res.status(200).send(result);
}

}
