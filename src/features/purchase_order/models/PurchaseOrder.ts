import { InterfaceMetaData } from "../../../core/CoreInterfaces";
import { ISingleRawMaterialDetails } from "../../raw_material/models/IRawMaterial";
import { ISupplier } from "../../supplier/models/ISupplier";
import { PurchaseOrderTable } from "../database/PurchaseOrderTable";


interface IPurchaseOrder extends InterfaceMetaData {
    poNumber:string;
    supplierId:number;
    expectedDate:Date;
    receivedDate:Date | null;
}

interface IPurchaseOrderItems {
    rawMaterialId: number;
    qty: number;
}


interface IPurchaseOrderPayload extends IPurchaseOrder {
    items:IPurchaseOrderItems[];
}

interface IUpdatePurchaseOrderPayload extends IPurchaseOrderPayload{
    id:number;
}

interface IPurchaseOrderResponse {
    id:number,
    poNumber:string,
    supplier: ISupplier,
    expectedDate:Date,
    items:IPurchaseOrderResponseItems []
}

interface IPurchaseOrderResponseItems {
    item:ISingleRawMaterialDetails,
    qty:number;
}


const purchaseOrderParser = (data:PurchaseOrderTable) :IPurchaseOrderResponse =>{

    

    return {
        id:Number(data.dataValues.id),
        poNumber:data.dataValues.poNumber,
        supplier:{
            id:Number(data.dataValues.supplierId),
            name:data.supplier.dataValues.name,
            email:data.supplier.dataValues.email,
            phone:data.supplier.dataValues.phone,
            gst:data.supplier.dataValues.gst,
            pan:data.supplier.dataValues.pan,
            addressId:data.supplier.dataValues.addressId,
            status:data.supplier.dataValues.status,
            createdAt:data.supplier.dataValues.createdAt,
            updatedAt:data.supplier.dataValues.updatedAt,
            createdById:data.supplier.dataValues.createdById,
            updatedById:data.supplier.dataValues.updatedById,
            deletedAt:data.supplier.dataValues.deletedAt,
            deletedById:data.supplier.dataValues.deletedById
            },
            expectedDate:data.dataValues.expectedDate,
            items:data.purchaseOrderItems.map((item) =>{
                return {
                 item:{
                    id:Number(item.rawMaterialVariation.dataValues.id),
                    name:item.rawMaterialVariation.dataValues.name,
                    unitName:item.rawMaterialVariation.rawMaterial.unit.dataValues.name,
                    categoryName:item.rawMaterialVariation.rawMaterial.category.dataValues.name,
                    price:0,
                    parentRawMaterialId:1,
                    deletedAt:item.rawMaterialVariation.dataValues.deletedAt,
                    deletedById:item.rawMaterialVariation.dataValues.deletedById,
                    status:item.rawMaterialVariation.dataValues.status,
                    sku:item.rawMaterialVariation.dataValues.sku,
                    moq:item.rawMaterialVariation.dataValues.moq,
                    unitId:item.rawMaterialVariation.rawMaterial.dataValues.unitId,
                    categoryId:item.rawMaterialVariation.rawMaterial.dataValues.categoryId,
                    createdAt:item.rawMaterialVariation.dataValues.createdAt,
                    updatedAt:item.rawMaterialVariation.dataValues.updatedAt,
                    createdById:item.rawMaterialVariation.dataValues.createdById,
                    updatedById:item.rawMaterialVariation.dataValues.updatedById,
                    gstPercentage:item.rawMaterialVariation.rawMaterial.dataValues.gstPercentage,
                    msq:item.rawMaterialVariation.dataValues.msq,
                    hsn:item.rawMaterialVariation.rawMaterial.dataValues.hsn,
                 },
                    qty:Number(item.dataValues.qty),
                }

        })
    }
}

type CreatePurchaseOrder = Pick<IPurchaseOrder, "poNumber" | "supplierId" |"expectedDate" | "receivedDate" | "createdById">;

type UpdatePurchaseOrder = Pick<IPurchaseOrder, "poNumber" | "supplierId" |"expectedDate" | "receivedDate" | "updatedById">;

export { IPurchaseOrder, CreatePurchaseOrder, UpdatePurchaseOrder, IPurchaseOrderPayload, IUpdatePurchaseOrderPayload, IPurchaseOrderResponse, purchaseOrderParser , };