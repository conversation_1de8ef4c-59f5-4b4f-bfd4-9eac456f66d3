import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { PurchaseOrderTable } from "../database/PurchaseOrderTable";
import { IPurchaseOrderPayload, IPurchaseOrderResponse, IUpdatePurchaseOrderPayload} from "../models/PurchaseOrder";

export interface IPurchaseOrderRepo {
    create(payload: IPurchaseOrderPayload): Promise<APIBaseResponse< PurchaseOrderTable | null>>;

    update(payload: IUpdatePurchaseOrderPayload): Promise<APIBaseResponse<null>>;

    getAll(page: number, pageSize: number, text?: string): Promise<APIBaseResponse<PaginatedBaseResponse<IPurchaseOrderResponse> | null>>;

    getById(id: Number): Promise<APIBaseResponse<IPurchaseOrderResponse | null>>;

    delete(ids: Number[], deletedById: Number): Promise<APIBaseResponse<null>>;

    searchByPoNumber(text: string): Promise<APIBaseResponse<IPurchaseOrderResponse[] | null>>
}