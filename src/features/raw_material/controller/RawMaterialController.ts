import { NextFunction, Request, Response } from "express";
import { RepoProvider } from "../../../core/RepoProvider";
import { get, pick } from "lodash";
import { RAW_MATERIAL_STAUS } from "../models/RawMaterialMisc";
import { IRawMaterialAddPayload, IRawMaterialAddRequest, IRawMaterialDeleteRequest, IRawMaterialUpdateRequest, TAddVariationRequest, TDeleteVariationRequest, TUpdateVariationRequest } from "../models/IRawMaterialAndVariations";

export class RawMaterialController {

    static async create(req: Request, res: Response, next: NextFunction) {

        const userId = get(req, "user_id");

        const bodyData = req.body as IRawMaterialAddRequest;
        const payload: IRawMaterialAddPayload = {
            rawMaterial: {
                categoryId: bodyData.rawMaterial.categoryId,
                unitId: bodyData.rawMaterial.unitId,
                name: bodyData.rawMaterial.name.trim().toLowerCase(),
                hsn: bodyData.rawMaterial.hsn.trim().toLowerCase(),
                gstPercentage: bodyData.rawMaterial.gstPercentage,
            },
            variations: bodyData.variations.map(data => ({
                name: data.name.trim().toLowerCase(),
                sku: data.sku.trim().toLowerCase(),
                msq: data.msq,
                moq: data.moq,
                attributes: data.attributes.map(attribute => ({
                    attributeValueId: attribute.attributeValueId,
                })),
                priceData: data.priceData.map(priceData => ({
                    supplierId: priceData.supplierId,
                    price: priceData.price,
                })),
            })),
            createdById: Number(userId!),
        };
        const result = await RepoProvider.rawMaterialRepo.create(payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async updateRawMaterial(req: Request, res: Response, next: NextFunction) {

        const id = Number(get(req.params, "id"));
        const userId = get(req, "user_id",);
        const bodydata = req.body as IRawMaterialUpdateRequest;
        const payload: IRawMaterialUpdateRequest = {
            id: id,
            categoryId: bodydata.categoryId,
            unitId: bodydata.unitId,
            name: bodydata.name.trim().toLowerCase(),
            hsn: bodydata.hsn.trim().toLowerCase(),
            gstPercentage: bodydata.gstPercentage,
            updatedById: Number(userId!),
        };

        const result = await RepoProvider.rawMaterialRepo.updateRawMaterial(payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async deleteRawMaterials(req: Request, res: Response, next: NextFunction) {
        const bodyData: any = pick(req.body, "ids");
        const userId = get(req, "user_id",);
        const payload: IRawMaterialDeleteRequest = {
            ids: bodyData.ids,
            deletedById: Number(userId!),
        };
        const result = await RepoProvider.rawMaterialRepo.deleteRawMaterials(payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getRawMaterials(req: Request, res: Response, next: NextFunction) {

        const page = Number(get(req.query, "page"));
        const pageSize = Number(get(req.query, "pageSize"));
        let text = get(req.query, "text") as string | undefined;
        if (text) {
            text = text.trim().toLowerCase();
        }

        const result = await RepoProvider.rawMaterialRepo.getRawMaterials(page, pageSize, text);

        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getRawMaterialById(req: Request, res: Response, next: NextFunction) {
        const id = get(req.params, "id");
        const result = await RepoProvider.rawMaterialRepo.getRawMaterialById(Number(id));
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }
    static async addNewVariation(req: Request, res: Response, next: NextFunction) {
        const userId = get(req, "user_id",);
        const bodyData = req.body as TAddVariationRequest[];
        const payload: TAddVariationRequest[] = [];
        for (const data of bodyData) {
            payload.push({
                rawMaterialId: data.rawMaterialId,
                name: data.name.trim().toLowerCase(),
                sku: data.sku.trim().toLowerCase(),
                msq: data.msq,
                moq: data.moq,
                attributes: data.attributes,
                priceData: data.priceData,
                createdById: Number(userId!),
            });
        }

        const result = await RepoProvider.rawMaterialRepo.addNewVariations(payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }
    static async updateVariation(req: Request, res: Response, next: NextFunction) {
        const userId = get(req, "user_id",);
        const id = get(req.params, "id");
        const bodyData = req.body as TUpdateVariationRequest;
        const payload: TUpdateVariationRequest = {
            id: Number(id),
            rawMaterialId: bodyData.rawMaterialId,
            name: bodyData.name.trim().toLowerCase(),
            sku: bodyData.sku.trim().toLowerCase(),
            msq: bodyData.msq,
            moq: bodyData.moq,
            attributes: bodyData.attributes,
            priceData: bodyData.priceData,
            updatedById: Number(userId!),
        };
        const result = await RepoProvider.rawMaterialRepo.updateVariation(payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }
    static async deleteVariation(req: Request, res: Response, next: NextFunction) {
        const userId = get(req, "user_id",);
        const bodyData = req.body as TDeleteVariationRequest;
        const payload: TDeleteVariationRequest = {
            ids: bodyData.ids,
            deletedById: Number(userId!),
        };
        const result = await RepoProvider.rawMaterialRepo.deleteVariation(payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }
    static async getVariations(req: Request, res: Response, next: NextFunction) {
        const page = Number(get(req.query, "page"));
        const pageSize = Number(get(req.query, "pageSize"));
        let text = get(req.query, "text") as string | undefined;
        if (text) {
            text = text.trim().toLowerCase();
        }
        const supplierId = get(req.query, "supplierId") as number | undefined;
        const result = await RepoProvider.rawMaterialRepo.getvariations(page, pageSize, text, supplierId);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }


}