import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { IRawMaterialDetails, IRawMaterialVariation, ISingleRawMaterialDetails } from "../models/IRawMaterial";
import { TAddVariationRequest, IRawMaterialAddPayload, IRawMaterialDeleteRequest, IRawMaterialNew, IRawMaterialOverview, IRawMaterialUpdateRequest, IRawMaterialVariationNested, TUpdateVariationRequest, TDeleteVariationRequest, IRawMaterialVariationResponse } from "../models/IRawMaterialAndVariations";

export interface IRawMaterialRepo {
    create(payload: IRawMaterialAddPayload): Promise<APIBaseResponse<null>>;
    getRawMaterials(page: number, pageSize: number, text?: string): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialOverview> | null>>;
    getRawMaterialById(id: number): Promise<APIBaseResponse<IRawMaterialNew | null>>;
    updateRawMaterial(payload: IRawMaterialUpdateRequest): Promise<APIBaseResponse<null>>;
    deleteRawMaterials(payload: IRawMaterialDeleteRequest): Promise<APIBaseResponse<null>>;
    addNewVariations(payload: TAddVariationRequest[]): Promise<APIBaseResponse<IRawMaterialVariationNested[] | null>>;
    updateVariation(payload: TUpdateVariationRequest): Promise<APIBaseResponse<IRawMaterialVariationNested | null>>;
    deleteVariation(payload: TDeleteVariationRequest): Promise<APIBaseResponse<null>>;
    getvariations(page: number, pageSize: number, text?: string,supplierId?: number): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialVariationResponse> | null>>;
}