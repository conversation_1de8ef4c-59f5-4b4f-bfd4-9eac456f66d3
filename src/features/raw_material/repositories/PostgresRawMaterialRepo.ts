import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { HelperMethods } from "../../../core/HelperMethods";
import { sequelizeInit } from "../../../sequelize_init";
import { ItemCategoryTable } from "../../item_category/database/ItemCategoryTable";
import { ItemUnitTable } from "../../item_unit/database/ItemUnitTable";
import { RawMaterialStockTable } from "../../raw_material_stock/database/RawMaterialStockTable";
import { RawMaterialVariationTable } from "../database/RawMaterialVariationTable";
import { RAW_MATERIAL_STAUS } from "../models/RawMaterialMisc";
import { IRawMaterialRepo } from "./IRawMaterialRepo";
import { Op, Transaction, UniqueConstraintError, WhereOptions } from "sequelize";
import { RawMaterialPriceTable } from "../database/RawMaterialPriceTable";
import { RawMaterialMainTable } from "../database/RawMaterialMainTable";
import { RawMaterialVariationsAndAttributesRelationTable } from "../database/RawMaterialsAndAttributesRelationTable";
import { TAddVariationRequest, IRawMaterialAddPayload, IRawMaterialDeleteRequest, IRawMaterialNew, IRawMaterialOverview, IRawMaterialUpdateRequest, IRawMaterialVariationNested, TUpdateVariationRequest, TDeleteVariationRequest, IRawMaterialVariationResponse, } from "../models/IRawMaterialAndVariations";
import { IRawMaterialMain } from "../models/IRawMaterialMain";
import { SupplierTable } from "../../supplier/database/SupplierTable";
import { ItemAttributeValueTable } from "../../item_attribute_value/database/ItemAttributeValueTable";
import { ItemAttributeTable } from "../../item_attribute/database/ItemAttributeTable";
import { IRawMaterialVariation } from "../models/IRawMaterial";
import { ISupplier } from "../../supplier/models/ISupplier";

export class PostgresRawMaterialRepo implements IRawMaterialRepo {

    async create(payload: IRawMaterialAddPayload): Promise<APIBaseResponse<null>> {
        const transaction = await sequelizeInit.transaction();
        try {
            /* create main raw material */
            const savedRawMaterial = await RawMaterialMainTable.create({
                ...payload.rawMaterial,
                status: RAW_MATERIAL_STAUS.ACTIVE,
                createdById: payload.createdById,
            }, {
                transaction: transaction,
                userId: payload.createdById,
            });

            let savedVariation;

            for (const data of payload.variations) {
                /* create variations */
                savedVariation = await RawMaterialVariationTable.create({
                    parentRawMaterialId: savedRawMaterial.dataValues.id,
                    name: data.name,
                    sku: data.sku,
                    msq: data.msq,
                    moq: data.moq,
                    status: RAW_MATERIAL_STAUS.ACTIVE,
                    createdById: payload.createdById,
                }, {
                    transaction: transaction,
                    userId: payload.createdById,
                });
                /* create attributes relations */
                for (const attribute of data.attributes) {
                    await RawMaterialVariationsAndAttributesRelationTable.create({
                        rawMaterialVariationId: savedVariation.dataValues.id,
                        itemAttributeValueId: attribute.attributeValueId,
                        createdById: payload.createdById,
                    }, {
                        transaction: transaction,
                        userId: payload.createdById,
                    });
                }

                /* create variation price data */
                for (const priceData of data.priceData) {
                    await RawMaterialPriceTable.create({
                        price: priceData.price,
                        moq: 0,
                        supplierId: priceData.supplierId,
                        rawMaterialId: savedVariation.dataValues.id,
                        createdById: payload.createdById,
                    }, {
                        transaction: transaction,
                        userId: payload.createdById,
                    });
                }

                /* create variation empty stock */
                await RawMaterialStockTable.create({
                    rawMaterialId: savedVariation.dataValues.id,
                    totalStock: 0,
                    assignedStock: 0,
                    usableStock: 0,
                    createdById: payload.createdById,
                }, {
                    transaction: transaction,
                    userId: payload.createdById,
                });
            }

            await transaction.commit();

            return HelperMethods.getSuccessResponse(null);
        } catch (error) {
            await transaction.rollback();
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                if (error.errors[0].path === 'name') {
                    return HelperMethods.getErrorResponse('Name already exists');
                }
                else if (error.errors[0].path === 'sku') {
                    return HelperMethods.getErrorResponse('SKU already exists');
                }
            }
            return HelperMethods.getErrorResponse();
        }
    }
    async getRawMaterials(page: number, pageSize: number, text?: string): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialOverview> | null>> {
        try {
            const offset = (page - 1) * pageSize;

            const whereConditions: WhereOptions<IRawMaterialMain> = {
                status: RAW_MATERIAL_STAUS.ACTIVE,
            };

            if (text) {
                whereConditions.name = {
                    [Op.iLike]: `%${text}%`
                };
            }

            const { count, rows } = await RawMaterialMainTable.findAndCountAll({
                limit: pageSize,
                offset: offset,
                order: [['createdAt', 'DESC']],
                where: whereConditions,
                include: [
                    {
                        model: ItemCategoryTable,
                        attributes: ['name'],
                        as: "category",
                    },
                    {
                        model: ItemUnitTable,
                        attributes: ['name'],
                        as: "unit",
                    },
                ],
            });

            const totalPages = Math.ceil(count / pageSize);
            const data: IRawMaterialOverview[] = [];
            for (const item of rows) {
                data.push({
                    id: item.dataValues.id,
                    name: item.dataValues.name,
                    hsn: item.dataValues.hsn,
                    gstPercentage: item.dataValues.gstPercentage,
                    unitName: item.unit.dataValues.name,
                    categoryName: item.category.dataValues.name,
                });
            }

            return HelperMethods.getSuccessResponse({
                currentPage: page,
                totalData: count,
                totalPages: totalPages,
                data: data,
            });
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }
    async getRawMaterialById(id: number): Promise<APIBaseResponse<IRawMaterialNew | null>> {
        try {
            const result = await RawMaterialMainTable.findByPk(id, {
                include: [
                    {
                        model: ItemCategoryTable,
                        attributes: ['id', 'name'],
                        as: "category",
                    },
                    {
                        model: ItemUnitTable,
                        attributes: ['id', 'name'],
                        as: "unit",
                    },
                ],
            });

            if (!result) {
                return HelperMethods.getErrorResponse("Raw Material not found");
            }

            const variations = await RawMaterialVariationTable.findAll({
                where: {
                    parentRawMaterialId: result.dataValues.id,
                },
                include: [
                    {
                        model: RawMaterialPriceTable,
                        as: "prices",
                        include: [
                            {
                                model: SupplierTable,
                                as: "supplier",
                            },
                        ],
                    },
                    {
                        model: RawMaterialVariationsAndAttributesRelationTable,
                        as: "attributesRelation",
                        include: [
                            {
                                model: ItemAttributeValueTable,
                                as: "attributeValue",
                                include: [
                                    {
                                        model: ItemAttributeTable,
                                        as: "attribute",
                                    },
                                ],
                            },
                        ],
                    },
                ],
            });

            const data: IRawMaterialNew = {
                rawMaterial: {
                    id: result.dataValues.id,
                    category: {
                        ...result.category.dataValues,
                        id: Number(result.category.dataValues.id),
                    },
                    unit: {
                        ...result.unit.dataValues,
                        id: Number(result.unit.dataValues.id),
                    },
                    name: result.dataValues.name,
                    hsn: result.dataValues.hsn,
                    gstPercentage: Number(result.dataValues.gstPercentage),
                    status: result.dataValues.status,
                },
                variations: variations.map(data => ({
                    id: Number(data.dataValues.id),
                    sku: data.dataValues.sku,
                    name: data.dataValues.name,
                    msq: data.dataValues.msq,
                    moq: data.dataValues.moq,
                    attributes: data.attributesRelation.map(data => ({
                        attribute: {
                            ...data.attributeValue.attribute.dataValues,
                            id: Number(data.attributeValue.attribute.dataValues.id),
                        },
                        attributeValue: {
                            ...data.attributeValue.dataValues,
                            id: Number(data.attributeValue.dataValues.id),
                        },
                    })),
                    priceData: data.prices.map(data => ({
                        supplier: {
                            ...data.supplier.dataValues,
                            id: Number(data.supplier.dataValues.id),
                        },
                        price: Number(data.dataValues.price),
                    })),
                })),
            };

            return HelperMethods.getSuccessResponse(data);
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }
    async updateRawMaterial(payload: IRawMaterialUpdateRequest): Promise<APIBaseResponse<null>> {
        try {
            /* checkif exits*/
            const existingRawMaterial = await RawMaterialMainTable.findOne({
                where: {
                    id: payload.id
                }
            });

            if (!existingRawMaterial) {
                return HelperMethods.getErrorResponse('Raw Material does not exist');
            }

            await RawMaterialMainTable.update({
                name: payload.name,
                hsn: payload.hsn,
                gstPercentage: payload.gstPercentage,
                categoryId: payload.categoryId,
                unitId: payload.unitId,
            }, {
                where: {
                    id: payload.id
                },
                userId: payload.updatedById!,
                individualHooks: true,

            });
            return HelperMethods.getSuccessResponse(null);
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }
    async deleteRawMaterials(payload: IRawMaterialDeleteRequest): Promise<APIBaseResponse<null>> {
        const transaction = await sequelizeInit.transaction();
        try {

            console.log(payload.ids);

            await RawMaterialMainTable.update({
                deletedAt: new Date(),
                deletedById: payload.deletedById,
            }, {
                where: {
                    id: {
                        [Op.in]: payload.ids
                    }
                },
                userId: payload.deletedById,
                individualHooks: true,
                transaction: transaction,
            });
            await RawMaterialVariationTable.update({
                deletedAt: new Date(),
                deletedById: payload.deletedById,
            }, {
                where: {
                    parentRawMaterialId: {
                        [Op.in]: payload.ids
                    }
                },
                userId: payload.deletedById,
                individualHooks: true,
                transaction: transaction,
            });
            await transaction.commit();
            return HelperMethods.getSuccessResponse(null);
        } catch (error) {
            await transaction.rollback();
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }
    async addNewVariations(payload: TAddVariationRequest[]): Promise<APIBaseResponse<IRawMaterialVariationNested[] | null>> {
        const transaction = await sequelizeInit.transaction();
        const responseData: IRawMaterialVariationNested[] = [];
        let fetchedData: IRawMaterialVariationNested | null;
        try {
            for (const data of payload) {
                /* create variations */
                const savedVariation = await RawMaterialVariationTable.create({
                    parentRawMaterialId: data.rawMaterialId,
                    name: data.name,
                    sku: data.sku,
                    msq: data.msq,
                    moq: data.moq,
                    status: RAW_MATERIAL_STAUS.ACTIVE,
                    createdById: data.createdById,
                }, {
                    transaction: transaction,
                    userId: data.createdById,
                });
                /* create attributes relations */
                for (const attribute of data.attributes) {
                    await RawMaterialVariationsAndAttributesRelationTable.create({
                        rawMaterialVariationId: savedVariation.dataValues.id,
                        itemAttributeValueId: attribute.attributeValueId,
                        createdById: data.createdById,
                    }, {
                        transaction: transaction,
                        userId: data.createdById,
                    });
                }

                /* create variation price data */
                for (const priceData of data.priceData) {
                    await RawMaterialPriceTable.create({
                        price: priceData.price,
                        moq: 0,
                        supplierId: priceData.supplierId,
                        rawMaterialId: savedVariation.dataValues.id,
                        createdById: data.createdById,
                    }, {
                        transaction: transaction,
                        userId: data.createdById,
                    });
                }
                /* create variation empty stock */
                await RawMaterialStockTable.create({
                    rawMaterialId: savedVariation.dataValues.id,
                    totalStock: 0,
                    assignedStock: 0,
                    usableStock: 0,
                    createdById: data.createdById,
                }, {
                    transaction: transaction,
                    userId: data.createdById,
                });
                fetchedData = await this.getVariationById(savedVariation.dataValues.id, transaction);
                if (fetchedData) {
                    responseData.push(fetchedData);
                }
            }
            await transaction.commit();
            return HelperMethods.getSuccessResponse(responseData);
        } catch (error) {
            await transaction.rollback();
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }
    async updateVariation(payload: TUpdateVariationRequest): Promise<APIBaseResponse<IRawMaterialVariationNested | null>> {
        const transaction = await sequelizeInit.transaction();
        try {
            /* checkif exits*/
            const existingVariation = await RawMaterialVariationTable.findOne({
                where: {
                    id: payload.id
                }
            });

            if (!existingVariation) {
                return HelperMethods.getErrorResponse('Variation does not exist');

            }
            /* delete all old relations to attributes */
            await RawMaterialVariationsAndAttributesRelationTable.destroy({
                where: {
                    rawMaterialVariationId: payload.id
                },
                force: true,
                transaction: transaction,
                userId: payload.updatedById,
                individualHooks: true,
            });

            /* create new relations */
            for (const attribute of payload.attributes) {
                await RawMaterialVariationsAndAttributesRelationTable.create({
                    rawMaterialVariationId: payload.id,
                    itemAttributeValueId: attribute.attributeValueId,
                    createdById: payload.updatedById,
                }, {
                    transaction: transaction,
                    userId: payload.updatedById,
                });
            }
            /* delete old price data */
            await RawMaterialPriceTable.destroy({
                where: {
                    rawMaterialId: payload.id
                },
                force: true,
                transaction: transaction,
                userId: payload.updatedById,
                individualHooks: true,
            });

            /* create new price data */
            for (const priceData of payload.priceData) {
                await RawMaterialPriceTable.create({
                    price: priceData.price,
                    moq: 0,
                    supplierId: priceData.supplierId,
                    rawMaterialId: payload.id,
                    createdById: payload.updatedById,
                }, {
                    transaction: transaction,
                    userId: payload.updatedById,
                });
            }
            /* update variation */
            await RawMaterialVariationTable.update({
                name: payload.name,
                sku: payload.sku,
                msq: payload.msq,
                moq: payload.moq,
            }, {
                where: {
                    id: payload.id
                },
                userId: payload.updatedById,
                individualHooks: true,
                transaction: transaction,
            });

            const data = await this.getVariationById(payload.id, transaction);
            await transaction.commit();
            return HelperMethods.getSuccessResponse(data);
        } catch (error) {
            await transaction.rollback();
            if (error instanceof UniqueConstraintError) {
                if (error.errors[0].path === 'sku') {
                    return HelperMethods.getErrorResponse('SKU "' + payload.sku + '" already exists');
                }
                else if (error.errors[0].path === 'name') {
                    return HelperMethods.getErrorResponse('Variation name "' + payload.name + '" already exists');
                }
            }
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }
    async getVariationById(id: number, transaction?: Transaction): Promise<IRawMaterialVariationNested | null> {
        try {
            const result = await RawMaterialVariationTable.findByPk(id, {
                transaction: transaction,
                include: [
                    {
                        model: RawMaterialPriceTable,
                        as: "prices",
                        include: [
                            {
                                model: SupplierTable,
                                as: "supplier",
                            },
                        ],
                    },
                    {
                        model: RawMaterialVariationsAndAttributesRelationTable,
                        as: "attributesRelation",
                        include: [
                            {
                                model: ItemAttributeValueTable,
                                as: "attributeValue",
                                include: [
                                    {
                                        model: ItemAttributeTable,
                                        as: "attribute",
                                    },
                                ],
                            },
                        ],
                    },
                ],
            });

            if (!result) {
                return null;
            }
            return {
                id: Number(result.dataValues.id),
                sku: result.dataValues.sku,
                name: result.dataValues.name,
                msq: result.dataValues.msq,
                moq: result.dataValues.moq,
                attributes: result.attributesRelation.map(data => ({
                    attribute: {
                        ...data.attributeValue.attribute.dataValues,
                        id: Number(data.attributeValue.attribute.dataValues.id),
                    },
                    attributeValue: {
                        ...data.attributeValue.dataValues,
                        id: Number(data.attributeValue.dataValues.id),
                    },
                })),
                priceData: result.prices.map(data => ({
                    supplier: {
                        ...data.supplier.dataValues,
                        id: Number(data.supplier.dataValues.id),
                    },
                    price: Number(data.dataValues.price),
                })),
            };
        } catch (error) {
            HelperMethods.handleError(error);
            return null;
        }
    }
    async deleteVariation(payload: TDeleteVariationRequest): Promise<APIBaseResponse<null>> {
        const transaction = await sequelizeInit.transaction();
        try {
            await RawMaterialVariationTable.update({
                deletedAt: new Date(),
                deletedById: payload.deletedById,
            }, {
                where: {
                    id: {
                        [Op.in]: payload.ids
                    }
                },
                userId: payload.deletedById,
                individualHooks: true,
                transaction: transaction,
            });
            await transaction.commit();
            return HelperMethods.getSuccessResponse(null);
        } catch (error) {
            await transaction.rollback();
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }
    async getvariations(page: number, pageSize: number, text?: string, supplierId?: number): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialVariationResponse> | null>> {
        try {
            const whereConditions: WhereOptions<IRawMaterialVariation> = {
                deletedAt: null
            };

            if (text) {
                whereConditions.name = {
                    [Op.iLike]: `%${text}%`
                };
            }

            const offset = (page - 1) * pageSize;
            const { count, rows } = await RawMaterialVariationTable.findAndCountAll({
                limit: pageSize,
                offset: offset,
                order: [['createdAt', 'DESC']],
                where: whereConditions,
                include: [
                    {
                        model: RawMaterialMainTable,
                        as: "rawMaterial",
                        include: [
                            {
                                model: ItemCategoryTable,
                                attributes: ['id', 'name'],
                                as: "category",
                            },
                            {
                                model: ItemUnitTable,
                                attributes: ['id', 'name'],
                                as: "unit",
                            },
                        ],
                    },
                    {
                        model: RawMaterialPriceTable,
                        as: "prices",
                        where: supplierId ? {
                            supplierId: supplierId
                        } : undefined,
                        include: [
                            {
                                model: SupplierTable,
                                as: "supplier",
                            },
                        ],
                    },
                ],
            });

            const totalPages = Math.ceil(count / pageSize);

            const data: IRawMaterialVariationResponse[] = [];
            for (const result of rows) {
                data.push({
                    id: Number(result.dataValues.id),
                    name: result.dataValues.name,
                    unit: result.rawMaterial.unit.dataValues,
                    category: result.rawMaterial.category.dataValues,
                    sku: result.dataValues.sku,
                    msq: result.dataValues.msq,
                    hsn: result.rawMaterial.dataValues.hsn,
                    gstPercentage: result.rawMaterial.dataValues.gstPercentage,
                    priceData: result.prices.map(data => ({
                        supplier: {
                            ...data.supplier.dataValues,
                            id: Number(data.supplier.dataValues.id),
                        },
                        price: Number(data.dataValues.price),
                    })),
                });
            }

            return HelperMethods.getSuccessResponse({
                currentPage: page,
                totalData: count,
                totalPages: totalPages,
                data: data,
            });
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }
}