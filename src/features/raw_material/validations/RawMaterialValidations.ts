import { Request, Response, NextFunction } from "express";
import { RawMaterialSchema } from "./RawMaterialSchema";
import { HelperMethods } from "../../../core/HelperMethods";
import { get, isArray, pick } from "lodash";
import { CoreSchemas } from "../../../core/CoreSchemas";
import { AddVariationRequestSchema, AddVariationRequestSchemaArray, DeleteVariationRequestSchema, UpdateVariationRequestSchema } from "../models/IRawMaterialAndVariations";

export class RawMaterialValidations {
    static validateCreate = (req: Request, res: Response, next: NextFunction) => {

        const result = RawMaterialSchema.create.safeParse(req.body);
        console.log("checked", req.body);

        console.log("result", result.error);

        let errorMessage = "";
        if (!result.success) {

            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0].toString().toUpperCase() + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }
        return next();
    }

    static validateUpdate = (req: Request, res: Response, next: NextFunction) => {

        const id = get(req.params, "id");
        if (!id) {
            res.status(400).send(HelperMethods.getErrorResponse("Id is required"));
            return;
        }
        else {
            try {
                parseInt(id);
            }
            catch (error) {
                res.status(400).send(HelperMethods.getErrorResponse("Id is not a number"));
                return;
            }
        }

        const result = RawMaterialSchema.update.safeParse(req.body);

        let errorMessage = "";
        if (!result.success) {

            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0].toString().toUpperCase() + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }
        return next();
    }

    static validateDelete = (req: Request, res: Response, next: NextFunction) => {

        const bodyData = pick(req.body, "ids");
        if (!bodyData || !isArray(bodyData.ids)) {
            res.status(400).send(HelperMethods.getErrorResponse("Ids are required"));
            return;
        }
        for (const id of bodyData.ids) {
            if (typeof id !== "number") {
                res.status(400).send(HelperMethods.getErrorResponse("Ids are required"));
                return;
            }
        }
        return next();
    }

    static validateGetAll = (req: Request, res: Response, next: NextFunction) => {

        const result = CoreSchemas.paginationSchema.safeParse(req.query);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid request"));
            return;
        }

        return next();
    }

    static validateGetById = (req: Request, res: Response, next: NextFunction) => {

        const id = get(req.params, "id");
        if (!id || typeof Number(id) !== "number") {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid id"));
            return;
        }

        return next();
    }


    static validateSearch = (req: Request, res: Response, next: NextFunction) => {

        const text = get(req.query, "text");
        if (!text) {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid request"));
            return;
        }
        const supplierId = get(req.query, "supplierId");
        if (supplierId && typeof Number(supplierId) !== "number") {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid request"));
            return;
        }

        return next();
    }
    static validateAddVariation = (req: Request, res: Response, next: NextFunction) => {

        const result = AddVariationRequestSchemaArray.safeParse(req.body);

        let errorMessage = "";
        if (!result.success) {

            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0].toString().toUpperCase() + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }
        return next();
    }
    static validateUpdateVariation = (req: Request, res: Response, next: NextFunction) => {
        let result: any = CoreSchemas.updateByIdSchema.safeParse(req.params);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }

        result = UpdateVariationRequestSchema.safeParse(req.body);
        let errorMessage = "";
        if (!result.success) {

            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0].toString().toUpperCase() + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }
        return next();
    }
    static validateDeleteVariation = (req: Request, res: Response, next: NextFunction) => {
        const result = DeleteVariationRequestSchema.safeParse(req.body);
        let errorMessage = "";
        if (!result.success) {

            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0].toString().toUpperCase() + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
        }
        return next();
    }
}