import { NextFunction, Request, Response } from "express";
import { RepoProvider } from "../../../core/RepoProvider";
import { get, pick } from "lodash";
import { IAssignStorageToStockRequest, ICreateRawMaterialStock, IRawMaterialStockUpdateRequest, IReceiveRawMaterialStock } from "../models/IRawMaterialStock";
import { ICreateRawMaterialStockIssuance } from "../models/IRawMaterialStockIssuance";

export class RawMaterialStockController {


    static async getAll(req: Request, res: Response, next: NextFunction) {

        const page = Number(get(req.query, "page"));
        const pageSize = Number(get(req.query, "pageSize"));
        const result = await RepoProvider.rawMaterialStockRepo.getAll(page, pageSize);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }



    static async getById(req: Request, res: Response, next: NextFunction) {
        const id = get(req.params, "id");
        const result = await RepoProvider.rawMaterialStockRepo.getById(Number(id));
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getByRawMaterialId(req: Request, res: Response, next: NextFunction) {
        const id = get(req.params, "id");
        const result = await RepoProvider.rawMaterialStockRepo.getByRawMaterialId(Number(id));
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getStockIn(req: Request, res: Response, next: NextFunction) {


        let startDate: Date | undefined;
        let endDate: Date | undefined;

        const startDateString = get(req.query, "startDate") as string | undefined;
        const endDateString = get(req.query, "endDate") as string | undefined;

        if (startDateString && endDateString) {

            startDate = new Date(startDateString);
            endDate = new Date(endDateString);

            // const startDateData = startDateString.split("-");
            // const endDateData = endDateString.split("-");

            // startDate = new Date(Number(startDateData[0]), Number(startDateData[1]) - 1, Number(startDateData[2]));

            // endDate = new Date(Number(endDateData[0]), Number(endDateData[1]) - 1, Number(endDateData[2]));
        }



        const page = Number(get(req.query, "page"));
        const pageSize = Number(get(req.query, "pageSize"));
        const text = get(req.query, "text") as string | undefined;

        const result = await RepoProvider.rawMaterialStockRepo.getStockIn(page, pageSize, text, startDate, endDate);

        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }


    static async getStockInWithoutStorage(req: Request, res: Response, next: NextFunction) {
        const page = Number(get(req.query, "page"));
        const pageSize = Number(get(req.query, "pageSize"));

        const result = await RepoProvider.rawMaterialStockRepo.getStockInWithoutStorage(page, pageSize);

        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }


    static async updateStock(req: Request, res: Response, next: NextFunction) {

        const id = Number(req.params.id);
        const userId = get(req, "user_id",);

        const payload: IRawMaterialStockUpdateRequest = {
            rawMaterialId: id,
            qty: Number(req.body.qty),
            updatedById: Number(userId!),
        };

        const result = await RepoProvider.rawMaterialStockRepo.updateStock(payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async assignStorageToStock(req: Request, res: Response, next: NextFunction) {

        const id = Number(req.params.id);
        const userId = get(req, "user_id",);

        const payload: IAssignStorageToStockRequest = {
            id: id,
            storageLocationId: Number(req.body.storageLocationId),
            updatedById: Number(userId!),
        };

        const result = await RepoProvider.rawMaterialStockRepo.assignStorageToStock(payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getStockInById(req: Request, res: Response, next: NextFunction) {
        const id = get(req.params, "id");
        const result = await RepoProvider.rawMaterialStockRepo.getStockInById(Number(id));
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async issueStock(req: Request, res: Response, next: NextFunction) {
        const userId = get(req, "user_id");

        const stockIssuances = req.body;

        const soNumber = req.body.soNumber as string;
        const notes = req.body.notes as string | null;
        const issuedToUserId = req.body.issuedToUserId as number;


        const payload: ICreateRawMaterialStockIssuance[] = stockIssuances.rawMaterials.map((item: any) => ({
            soNumber: soNumber,
            issuedTo: issuedToUserId,
            notes: notes,
            rawMaterialId: Number(item.rawMaterialId),
            qty: Number(item.qty),
            entryId: "",
            createdById: Number(userId!),
        }));

        const result = await RepoProvider.rawMaterialStockRepo.issueStock(payload);
        if (!result.success) {
            res.status(500).send(result);
        }
        res.status(200).send(result);
    }


    static async getAllStockIssuance(req: Request, res: Response, next: NextFunction) {

        const page = Number(get(req.query, "page"));
        const pageSize = Number(get(req.query, "pageSize"));
        const result = await RepoProvider.rawMaterialStockRepo.getAllStockIssuance(page, pageSize);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getStockInByDateRange(req: Request, res: Response, next: NextFunction) {

        const startDateString = get(req.query, "startDate") as string;
        const endDateString = get(req.query, "endDate") as string;

        const startDateData = startDateString.split("-");
        const endDateData = endDateString.split("-");

        const startDate = new Date(Number(startDateData[0]), Number(startDateData[1]) - 1, Number(startDateData[2]));
        const endDate = new Date(Number(endDateData[0]), Number(endDateData[1]) - 1, Number(endDateData[2]));

        const page = Number(get(req.query, "page"));
        const pageSize = Number(get(req.query, "pageSize"));


        const result = await RepoProvider.rawMaterialStockRepo.getStockInByDateRange(
            startDate,
            endDate,
            page, pageSize,);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async searchByRawMaterial(req: Request, res: Response) {
        const result = await RepoProvider.rawMaterialStockRepo.searchByRawMaterial(
            (req.query.rawMaterialName as string).toLowerCase(),
        );
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async searchInStockByText(req: Request, res: Response, next: NextFunction) {
        const result = await RepoProvider.rawMaterialStockRepo.searchInStockByText(
            (req.query.text as string).toLowerCase(),
            true,
        );
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async searchInStockByTextWithoutStorage(req: Request, res: Response, next: NextFunction) {
        const result = await RepoProvider.rawMaterialStockRepo.searchInStockByText(
            (req.query.text as string).toLowerCase(),
            false,

        );
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async searchStockIssuanceByText(req: Request, res: Response, next: NextFunction) {
        const result = await RepoProvider.rawMaterialStockRepo.searchStockIssuanceByText(
            (req.query.text as string).toLowerCase(),
        );
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async exportByCategory(req: Request, res: Response, next: NextFunction) {

        const categoryId = Number(get(req.params, "categoryId"));
        const result = await RepoProvider.rawMaterialStockRepo.exportByCategory(categoryId);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async exportByCategoryWithPrice(req: Request, res: Response, next: NextFunction) {

        const categoryId = Number(get(req.params, "categoryId"));
        const result = await RepoProvider.rawMaterialStockRepo.exportByCategoryWithPrice(categoryId);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }


    static async getStockIssuanceById(req: Request, res: Response, next: NextFunction) {
        const id = get(req.params, "id");
        const result = await RepoProvider.rawMaterialStockRepo.getStockIssuanceById(id);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }
}