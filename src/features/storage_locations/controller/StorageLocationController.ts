import { NextFunction, Request, Response } from "express";
import { RepoProvider } from "../../../core/RepoProvider";
import { get, pick } from "lodash";
import { STORAGE_LOCATION_STAUS } from "../models/StorageLocationMisc";
import { ICreateStorageLocation } from "../models/IStorageLocation";

export class StorageLocationController {

    static async create(req: Request, res: Response, next: NextFunction) {

        const userId = get(req, "user_id",);

        const payload = pick(req.body, ["name", "description", "type",]) as ICreateStorageLocation;
        payload.status = STORAGE_LOCATION_STAUS.ACTIVE;
        payload.createdById = Number(userId!);
        payload.name = payload.name.trim().toLowerCase();

        const result = await RepoProvider.storageLocationRepo.create(payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async update(req: Request, res: Response, next: NextFunction) {

        const id = Number(get(req.params, "id"));

        const userId = get(req, "user_id",);
        const payload = {
            ...req.body,
            updatedById: userId,
        };

        const result = await RepoProvider.storageLocationRepo.update(id, payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async delete(req: Request, res: Response, next: NextFunction) {
        const ids: any = pick(req.body, "ids");
        const userId = get(req, "user_id",);


        const result = await RepoProvider.storageLocationRepo.delete(ids, Number(userId));
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getAll(req: Request, res: Response, next: NextFunction) {

        const page = Number(get(req.query, "page"));
        const pageSize = Number(get(req.query, "pageSize"));
        const result = await RepoProvider.storageLocationRepo.getAll(page, pageSize);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async searchByText(req: Request, res: Response, next: NextFunction) {

        const text = String(get(req.query, "text"));
        const result = await RepoProvider.storageLocationRepo.searchByText(text);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getById(req: Request, res: Response, next: NextFunction) {
        const id = get(req.params, "id");
        const result = await RepoProvider.storageLocationRepo.getById(Number(id));
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }
}