import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { HelperMethods } from "../../../core/HelperMethods";
import { sequelizeInit } from "../../../sequelize_init";
import { AddressTable } from "../../address/database/AddressTable";
import { ADDRESS_STATUS } from "../../address/models/AddressMisc";
import { SupplierTable } from "../database/SupplierTable";
import { ICreateSupplier, ISupplierRequest, ISupplierResponse, ISupplierUpdateRequest } from "../models/ISupplier";
import { SUPPLIER_STAUS } from "../models/SupplierMisc";
import { ISupplierRepo } from "./ISupplierRepo";
import { Op, UniqueConstraintError } from "sequelize";

export class PostgresSupplierRepo implements ISupplierRepo {
    delete(ids: number[], deletedById: number): Promise<APIBaseResponse<null>> {
        throw new Error("Method not implemented.");
    }

    async create(supplier: ISupplierRequest): Promise<APIBaseResponse<SupplierTable | null>> {
        const transaction = await sequelizeInit.transaction();
        try {
            /* save address */
            const address = await AddressTable.create({
                ...supplier.address,
                status: ADDRESS_STATUS.ACTIVE,
                createdById: supplier.createdById,
            }, {
                transaction: transaction,
                userId: supplier.createdById,
            });

            const payload: ICreateSupplier = {
                name: supplier.name,
                email: supplier.email,
                phone: supplier.phone,
                addressId: address.dataValues.id,
                gst: supplier.gst,
                pan: supplier.pan,
                status: SUPPLIER_STAUS.ACTIVE,
                createdById: supplier.createdById,
            };


            const result = await SupplierTable.create(payload, {
                transaction: transaction,
                userId: supplier.createdById,
            });

            transaction.commit();

            return HelperMethods.getSuccessResponse(result);
        } catch (error) {
            await transaction.rollback();
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                if (error.errors[0].path === 'email') {
                    return HelperMethods.getErrorResponse('Email already exists');
                }
                else if (error.errors[0].path === 'phone') {
                    return HelperMethods.getErrorResponse('Contact number already exists');
                }
                else if (error.errors[0].path === 'gst') {
                    return HelperMethods.getErrorResponse('GST number already exists');
                }
                else if (error.errors[0].path === 'pan') {
                    return HelperMethods.getErrorResponse('PAN number already exists');
                }
            }
            return HelperMethods.getErrorResponse();
        }
    }

    async update(id: number, supplier: ISupplierUpdateRequest): Promise<APIBaseResponse<null>> {
        const transaction = await sequelizeInit.transaction();
        try {

            const deletionUpdates = {
                deletedAt: new Date(),
                deletedById: supplier.updatedById,
            };
            if (supplier.status === SUPPLIER_STAUS.DELETED) {
                Object.assign(supplier, deletionUpdates);
            }
            await SupplierTable.update(supplier, {
                where: {
                    id: id
                },
                transaction: transaction,
                userId: supplier.updatedById,
                individualHooks: true,
            });

            await AddressTable.update(supplier.address, {
                where: {
                    id: supplier.addressId
                },
                transaction: transaction,
                userId: supplier.updatedById!,
                individualHooks: true,

            });


            await transaction.commit();

            return HelperMethods.getSuccessResponse(null);
        } catch (error) {
            await transaction.rollback();
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                if (error.errors[0].path === 'email') {
                    return HelperMethods.getErrorResponse('Email already exists');
                }
                else if (error.errors[0].path === 'phone') {
                    return HelperMethods.getErrorResponse('Contact number already exists');
                }
                else if (error.errors[0].path === 'gst') {
                    return HelperMethods.getErrorResponse('GST number already exists');
                }
                else if (error.errors[0].path === 'pan') {
                    return HelperMethods.getErrorResponse('PAN number already exists');
                }
            }
            return HelperMethods.getErrorResponse();
        }
    }

    async getAll(page: number, pageSize: number): Promise<APIBaseResponse<PaginatedBaseResponse<SupplierTable> | null>> {
        try {
            const offset = (page - 1) * pageSize;
            const { count, rows } = await SupplierTable.findAndCountAll({
                limit: pageSize,
                offset: offset,
                order: [['createdAt', 'DESC']],
                where: {
                    status: SUPPLIER_STAUS.ACTIVE
                },
            });

            const totalPages = Math.ceil(count / pageSize);

            return HelperMethods.getSuccessResponse({
                currentPage: page,
                totalData: count,
                totalPages: totalPages,
                data: rows,
            });
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async searchByText(text: string): Promise<APIBaseResponse<PaginatedBaseResponse<SupplierTable> | null>> {
        try {
            const offset = 0;
            const { count, rows } = await SupplierTable.findAndCountAll({
                limit: 1,
                offset: offset,
                order: [['createdAt', 'DESC']],
                where: {
                    [Op.or]: [
                        {
                            name: {
                                [Op.iLike]: `%${text}%`
                            }
                        },
                        {
                            email: {
                                [Op.iLike]: `%${text}%`
                            }
                        },
                        {
                            phone: {
                                [Op.iLike]: `%${text}%`
                            }
                        },
                        {
                            gst: {
                                [Op.iLike]: `%${text}%`
                            },
                        }, {
                            pan: {
                                [Op.iLike]: `%${text}%`
                            },
                        }],



                    status: SUPPLIER_STAUS.ACTIVE
                },
            });

            const totalPages = 1;

            return HelperMethods.getSuccessResponse({
                currentPage: 1,
                totalData: count,
                totalPages: totalPages,
                data: rows,
            });
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async getById(id: number): Promise<APIBaseResponse<ISupplierResponse | null>> {
        try {
            const result = await SupplierTable.findByPk(id, {
                include: {
                    model: AddressTable,
                    as: "address",
                },
            });
            if (result) {
                return HelperMethods.getSuccessResponse({
                    ...result.get(),
                    address: result.address.get(),
                });
            }
            else {
                return HelperMethods.getErrorResponse("No record found");
            }
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }



    async search(text: string): Promise<APIBaseResponse<SupplierTable[] | null>> {
        try {
            const result = await SupplierTable.findAll({
                where: {
                    name: {
                        [Op.iLike]: `%${text}%`
                    }
                },
                limit: 5,
            });
            return HelperMethods.getSuccessResponse(result);
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }
}