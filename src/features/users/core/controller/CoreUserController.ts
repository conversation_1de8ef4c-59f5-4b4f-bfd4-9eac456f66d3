import { NextFunction, Request, Response } from "express";
import { get } from "lodash";
import { ICreateCoreUser, IUpdateCoreUser } from "../models/ICoreUser";
import { RepoProvider } from "../../../../core/RepoProvider";
import { USER_STAUS } from "../models/UserMisc";

export class CoreUserController {

    static async create(req: Request, res: Response, next: NextFunction) {

        const userId = get(req, "user_id",);

        const requestBody = req.body as ICreateCoreUser;

        const payload: ICreateCoreUser = {
            firstName: requestBody.firstName,
            lastName: requestBody.lastName,
            email: requestBody.email,
            password: req.body.password,
            mobile: requestBody.mobile,
            roleId: requestBody.roleId,
            firebaseUID: "",
            status: USER_STAUS.ACTIVE,
            createdById: Number(userId!),
        };

        const result = await Repo<PERSON>rovider.coreUserRepo.create(payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async update(req: Request, res: Response, next: NextFunction) {


        const userId = get(req, "user_id",);
        const id = Number(get(req.params, "id"));
        const requestBody = req.body as IUpdateCoreUser;

        const payload: IUpdateCoreUser = {
            id: id,
            firstName: requestBody.firstName,
            lastName: requestBody.lastName,
            mobile: requestBody.mobile,
            updatedById: Number(userId!),
            roleId: requestBody.roleId,
            status: requestBody.status,
            email: requestBody.email,
        };

        const result = await RepoProvider.coreUserRepo.update(payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getAll(req: Request, res: Response, next: NextFunction) {

        const page = Number(get(req.query, "page"));
        const pageSize = Number(get(req.query, "pageSize"));
        const text = get(req.query, "text") as string | undefined;

        const result = await RepoProvider.coreUserRepo.getAll(page, pageSize, text);

        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getById(req: Request, res: Response, next: NextFunction) {
        const id = get(req.params, "id");
        const result = await RepoProvider.coreUserRepo.getById(Number(id));
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getByFirebaseToken(req: Request, res: Response, next: NextFunction) {
        const userId = get(req, "user_id",);
        const result = await RepoProvider.coreUserRepo.getById(Number(userId!));
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }
}