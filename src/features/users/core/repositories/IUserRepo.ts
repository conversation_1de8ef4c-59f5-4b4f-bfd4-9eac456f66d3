import { APIBaseResponse, PaginatedBaseResponse } from "../../../../core/CoreInterfaces";
import { CoreUserTable } from "../database/CoreUserTable";
import { ICreateCoreUser, IUpdateCoreUser } from "../models/ICoreUser";

export interface ICoreUserRepo {
    create(payload: ICreateCoreUser): Promise<APIBaseResponse<CoreUserTable | null>>;

    update(payload: IUpdateCoreUser): Promise<APIBaseResponse<null>>;

    getAll(page: number, pageSize: number, text?: string): Promise<APIBaseResponse<PaginatedBaseResponse<CoreUserTable> | null>>;

    getById(id: Number): Promise<APIBaseResponse<CoreUserTable | null>>;
}