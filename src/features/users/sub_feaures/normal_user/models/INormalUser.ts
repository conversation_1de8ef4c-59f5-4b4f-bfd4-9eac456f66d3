import { AuditData } from "../../../../../core/CoreInterfaces";
import { <PERSON><PERSON><PERSON><PERSON>, ICreateAddress, IUpdateAddress } from "../../../../address/models/IAddress";
import { ICreateCoreUser, } from "../../../core/models/ICoreUser";

interface ICreateNormalUser extends ICreate<PERSON><PERSON>User {
    address: ICreateAddress;
}

interface IUpdateNormalUser extends Omit<ICreateNormalUser, "createdById" | "password" | "firebaseUID" | "address"> {
    id: number;
    address: IUpdateAddress;
    updatedById: number;
}

interface INormalUser {
    id: number;
    coreUserId: number;
    addressId: number;
}

interface INormalUserResponse extends Omit<ICreateNormalUser, "password" | "address" | "roleId" | "firebaseUID" | "createdById"> {
    id: number;
    coreUserId: number;
    address: IAddress;
    role: {
        id: Number,
        role: string,
    };
}


interface ResetPasswordPayload {
    userId:number;
    password: string;
    updatedById: number;
}


export { <PERSON><PERSON><PERSON><PERSON>ormal<PERSON><PERSON>, IUpdate<PERSON><PERSON>al<PERSON><PERSON>, <PERSON>ormal<PERSON>ser, INormal<PERSON>serResponse ,ResetPasswordPayload};