enum USER_ROLE_PERMISSIONS {
    CREATE = 'create#user_role',
    READ = 'read#user_role',
    UPDATE = 'update#user_role',
    // DELETE = 'delete_user_role',
}

enum USER_PERMISSIONS {
    CREATE = 'create#user',
    READ = 'read#user',
    UPDATE = 'update#user',
    PASSWORD_RESET = 'password_reset#user',
    // DELETE = 'delete_user',
}

enum SUPPLIER_PERMISSIONS {
    CREATE = 'create#supplier',
    READ = 'read#supplier',
    UPDATE = 'update#supplier',
    // DELETE = 'delete_supplier',
}
enum STORAGE_LOCATION_PERMISSIONS {
    CREATE = 'create#storage_location',
    READ = 'read#storage_location',
    UPDATE = 'update#storage_location',
    // DELETE = 'delete_storage_location',
}

enum FACTORY_GATE_PERMISSIONS {
    CREATE = 'create#factory_gate',
    READ = 'read#factory_gate',
    UPDATE = 'update#factory_gate',
    // DELETE = 'delete_factory_gate',
}

enum ITEM_CATEGORY_PERMISSIONS {
    CREATE = 'create#item_category',
    READ = 'read#item_category',
    UPDATE = 'update#item_category',
    // DELETE = 'delete_item_category',
}

enum ITEM_UNIT_PERMISSIONS {
    CREATE = 'create#item_unit',
    READ = 'read#item_unit',
    UPDATE = 'update#item_unit',
    // DELETE = 'delete_item_unit',
}

enum RAW_MATERIAL_PERMISSIONS {
    CREATE = 'create#raw_material',
    READ = 'read#raw_material',
    UPDATE = 'update#raw_material',
    // DELETE = 'delete_raw_material',
}

enum RAW_MATERIAL_STOCK_PERMISSIONS {
    RECEIVE = 'receive_stock#raw_material_stock',
    READ_CURRENT_STOCK = 'read_current_stock#raw_material_stock',
    UPDATE_CURRENT_STOCK = 'update_current_stock#raw_material_stock',
    READ_OPENING_STOCK = 'read_opening_stock#raw_material_stock',
    ADD_OPENING_STOCK = 'add_opening_stock#raw_material_stock',
    UPDATE_OPENING_STOCK = 'update_opening_stock#raw_material_stock',
    READ_STOCK_IN = 'read_stock_in#raw_material_stock',
    ASSIGN_STORAGE = 'assign_storage#raw_material_stock',
    READ_ISSUANCE = 'read_issuance#raw_material_stock',
    CREATE_ISSUANCE = 'create_issuance#raw_material_stock',
    READ_PURCHASE = 'read_purchase#raw_material_stock',
    UPDATE_PURCHASE = 'update_purchase#raw_material_stock',
    UPDATE_PURCHASE_FULL = 'update_purchase_full#raw_material_stock',
    // DELETE = 'delete_raw_material_stock',
}

enum DEBIT_NOTE_PERMISSIONS {
    CREATE = 'create#debit_note',
    READ = 'read#debit_note',
    UPDATE = 'update#debit_note',
    PRINT = 'print#debit_note',
    // DELETE = 'delete_user',
}

enum LOG_PERMISSIONS {
    READ = 'read#log',
}

enum STOCK_ADJUSTMENT_PERMISSIONS {
    CREATE = 'create#stock_adjustment',
    READ = 'read#stock_adjustment',
    UPDATE = 'update#stock_adjustment',
    // DELETE = 'delete_opening_stock',
}



abstract class AppPermissions {
    static USER_ROLE = USER_ROLE_PERMISSIONS;
    static USER = USER_PERMISSIONS;
    static SUPPLIER = SUPPLIER_PERMISSIONS;
    static STORAGE_LOCATION = STORAGE_LOCATION_PERMISSIONS;
    static FACTORY_GATE = FACTORY_GATE_PERMISSIONS;
    static ITEM_CATEGORY = ITEM_CATEGORY_PERMISSIONS;
    static ITEM_UNIT = ITEM_UNIT_PERMISSIONS;
    static RAW_MATERIAL = RAW_MATERIAL_PERMISSIONS;
    static RAW_MATERIAL_STOCK = RAW_MATERIAL_STOCK_PERMISSIONS;
    static DEBIT_NOTE = DEBIT_NOTE_PERMISSIONS;
    static LOG = LOG_PERMISSIONS;
    static STOCK_ADJUSTMENT = STOCK_ADJUSTMENT_PERMISSIONS;

}


export {
    USER_ROLE_PERMISSIONS,
    USER_PERMISSIONS,
    SUPPLIER_PERMISSIONS,
    STORAGE_LOCATION_PERMISSIONS,
    FACTORY_GATE_PERMISSIONS,
    ITEM_CATEGORY_PERMISSIONS,
    ITEM_UNIT_PERMISSIONS,
    RAW_MATERIAL_PERMISSIONS,
    RAW_MATERIAL_STOCK_PERMISSIONS,
    DEBIT_NOTE_PERMISSIONS,
    LOG_PERMISSIONS,
    AppPermissions,
    STOCK_ADJUSTMENT_PERMISSIONS,
};

