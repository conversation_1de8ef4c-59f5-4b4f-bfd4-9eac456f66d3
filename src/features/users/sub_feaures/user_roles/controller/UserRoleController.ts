import { NextFunction, Request, Response } from "express";
import { get } from "lodash";
import { ICreateUserRole, ICreateUserRoleWithPermissions, IUpdateUserRole, IUpdateUserRoleWithPermissions } from "../models/IUserRole";
import { USER_ROLE_STATUS } from "../models/UserRolesMisc";
import { RepoProvider } from "../../../../../core/RepoProvider";

export class UserRoleController {

    static async create(req: Request, res: Response, next: NextFunction) {

        const userId = get(req, "user_id");

        const requestBody = req.body as ICreateUserRoleWithPermissions;

        const payload: ICreateUserRoleWithPermissions = {
            role: requestBody.role,
            status: USER_ROLE_STATUS.ACTIVE,
            createdById: Number(userId!),
            permissions: requestBody.permissions,
        };

        const result = await RepoProvider.userRoleRepo.createWithPermissions(payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async update(req: Request, res: Response, next: NextFunction) {


        const userId = get(req, "user_id");
        const id = Number(get(req.params, "id"));
        const requestBody = req.body as IUpdateUserRoleWithPermissions;

        const payload: IUpdateUserRoleWithPermissions = {
            id: id,
            role: requestBody.role,
            status: requestBody.status,
            updatedById: Number(userId!),
            permissions: requestBody.permissions,
        };

        const result = await RepoProvider.userRoleRepo.updateWithPermissions(payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getAll(req: Request, res: Response, next: NextFunction) {

        const page = Number(get(req.query, "page"));
        const pageSize = Number(get(req.query, "pageSize"));
        const text = get(req.query, "text") as string | undefined;

        const result = await RepoProvider.userRoleRepo.getAll(page, pageSize, text);

        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getById(req: Request, res: Response, next: NextFunction) {
        const id = get(req.params, "id");
        const result = await RepoProvider.userRoleRepo.getById(Number(id));
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }
}