import { z } from "zod";
import { USER_ROLE_STATUS } from "../models/UserRolesMisc";
import { DEBIT_NOTE_PERMISSIONS, FACTORY_GATE_PERMISSIONS, ITEM_CATEGORY_PERMISSIONS, ITEM_UNIT_PERMISSIONS, LOG_PERMISSIONS, STOCK_ADJUSTMENT_PERMISSIONS, RAW_MATERIAL_PERMISSIONS, RAW_MATERIAL_STOCK_PERMISSIONS, STORAGE_LOCATION_PERMISSIONS, SUPPLIER_PERMISSIONS, USER_PERMISSIONS, USER_ROLE_PERMISSIONS } from "../../user_permissions/AppPermissions";
import { RAW_MATERIAL_STAUS } from "../../../../raw_material/models/RawMaterialMisc";

export class UserRoleSchema {
    static createSchema =
        z.object({
            role: z.string({ errorMap: () => ({ message: "Role is required" }) })
                .min(3, "Role must be at least 3 characters").max(255, "Role must be upto 255 characters").refine(val => !val.toLowerCase().trim().includes("admin"), { message: "Invalid role. Please donot use 'admin' word" }),
            permissions: z.array(z.union([
                z.nativeEnum(USER_ROLE_PERMISSIONS),
                z.nativeEnum(USER_PERMISSIONS),
                z.nativeEnum(SUPPLIER_PERMISSIONS),
                z.nativeEnum(STORAGE_LOCATION_PERMISSIONS),
                z.nativeEnum(FACTORY_GATE_PERMISSIONS),
                z.nativeEnum(ITEM_CATEGORY_PERMISSIONS),
                z.nativeEnum(ITEM_UNIT_PERMISSIONS),
                z.nativeEnum(RAW_MATERIAL_PERMISSIONS),
                z.nativeEnum(RAW_MATERIAL_STOCK_PERMISSIONS),
                z.nativeEnum(DEBIT_NOTE_PERMISSIONS),
                z.nativeEnum(LOG_PERMISSIONS),
                z.nativeEnum(STOCK_ADJUSTMENT_PERMISSIONS),

            ]), {
                errorMap: () => ({
                    message: "Permissions are required",
                })
            }),
        });

    static updateSchema =
        UserRoleSchema.createSchema.extend({
            status: z.nativeEnum(USER_ROLE_STATUS, {
                errorMap: () => ({
                    message: "Status is required",
                })
            }).refine(val => Object.values(USER_ROLE_STATUS).includes(val), {
                message: "Invalid status",
            }),
        });

}

