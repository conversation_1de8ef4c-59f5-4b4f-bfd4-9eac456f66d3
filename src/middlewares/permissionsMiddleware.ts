
import { NextFunction, Request, Response } from "express";
import { HelperMethods } from "../core/HelperMethods";

export const permissionsMiddleware = (permission: string) => async (req: Request, res: Response, next: NextFunction) => {

    const userPermissions = (req as any).user_permissions ?? [];
    if (!userPermissions.includes(permission)) {
        res.status(401).send(HelperMethods.getUnAuthorisedResponse());
        return;
    }
    next();
};
