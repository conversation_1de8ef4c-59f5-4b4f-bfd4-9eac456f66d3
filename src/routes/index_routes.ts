import { factoryGatesRouter } from "../features/factory_gates/routes/FactoryGateRoutes";
import { itemAttributeValueRouter } from "../features/item_attribute_value/routes/ItemAttributeRoutes";
import { itemAttributeRouter } from "../features/item_attribute/routes/ItemAttributeRoutes";
import { itemCategoryRouter } from "../features/item_category/routes/ItemCategoryRoutes";
import { itemUnitRouter } from "../features/item_unit/routes/ItemUnitRoutes";
import { rawMaterialRouter } from "../features/raw_material/routes/RawMaterialRoutes";
import { storageLocationRouter } from "../features/storage_locations/routes/StorageLocationRoutes";
import { supplierRouter } from "../features/supplier/routes/SupplierRoutes";
import { rawMaterialStockRouter } from "../features/raw_material_stock/routes/RawMaterialStockRoutes";
import { purchaseInvoicesRouter } from "../features/purchase_invoice/routes/PurchaseInvoiceRoutes";
import { debitNoteRouter } from "../features/debit_note/routes/DebitNoteRoutes";
import { logsRouter } from "../features/logs/routes/LogsRoutes";
import { userRoleRouter } from "../features/users/sub_feaures/user_roles/routes/UserRoleRoutes";
import { normalUserRouter } from "../features/users/sub_feaures/normal_user/routes/NormalUserRoutes";
import { openingStockRouter } from '../features/opening_stock/routes/OpeningStockRoutes'
import { purchaseOrderRouter } from "../features/purchase_order/routes/PurchaseOrderRoutes";
import { excelProcessorRouter } from "../features/excel_processor/routes/ExcelProcessorRoutes";


const routes = [
  supplierRouter,
  storageLocationRouter,
  factoryGatesRouter,
  rawMaterialRouter,
  itemCategoryRouter,
  itemAttributeRouter,
  itemAttributeValueRouter,
  itemUnitRouter,
  rawMaterialStockRouter,
  purchaseInvoicesRouter,
  debitNoteRouter,
  logsRouter,
  userRoleRouter,
  normalUserRouter,
  openingStockRouter,
  purchaseOrderRouter,
  // excelProcessorRouter,
];

export { routes }
